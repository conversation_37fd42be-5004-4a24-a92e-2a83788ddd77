"""
Пример расширенной конфигурации для Telegram фермы
Скопируйте нужные настройки в config.py
"""

# =============================================================================
# РАСШИРЕННЫЕ ШАБЛОНЫ КОММЕНТАРИЕВ
# =============================================================================

# Тематические шаблоны для разных типов контента
TECH_COMMENT_TEMPLATES = [
    "{Интересная|Полезная|Крутая} {технология|разработка|идея}! {Спасибо за обзор|Благодарю за информацию}!",
    "{Актуально|Важно|Полезно} для {разработчиков|программистов|IT-сферы}. {Сохраню|Добавлю в закладки}!",
    "{Хорошее|Качественное|Подробное} объяснение! {Все понятно|Доступно изложено}.",
]

BUSINESS_COMMENT_TEMPLATES = [
    "{Дельный|Полезный|Практичный} совет! {Применю в работе|Возьму на заметку}.",
    "{Согласен|Поддерживаю} с {подходом|мнением|стратегией}. {Работает|Проверено на практике}!",
    "{Интересный|Любопытный} {кейс|опыт|пример}. {Спасибо за детали|Благодарю за разбор}!",
]

LIFESTYLE_COMMENT_TEMPLATES = [
    "{Красиво|Стильно|Эстетично}! {Вдохновляет|Мотивирует|Поднимает настроение} 😊",
    "{Отличная|Классная|Крутая} {идея|находка|подборка}! {Обязательно попробую|Возьму на вооружение}.",
    "{Атмосферно|Уютно|По-домашнему}! {Создает настроение|Передает эмоции} ✨",
]

# =============================================================================
# РАСШИРЕННЫЕ ШАБЛОНЫ ОТВЕТОВ
# =============================================================================

AGREEMENT_REPLY_TEMPLATES = [
    "{Полностью согласен|Поддерживаю|Разделяю мнение}! {Сам так думаю|У меня похожий опыт}.",
    "{Да|Верно|Точно}, {это важно|это ключевой момент|это основа}!",
    "{Правильно говоришь|Хорошо подметил|Точно сформулировал}! {Добавлю от себя|Дополню мысль}...",
]

QUESTION_REPLY_TEMPLATES = [
    "{Интересный вопрос|Хороший вопрос|Актуальная тема}! {Тоже интересует|Жду ответов экспертов}.",
    "{А что думаешь|А как считаешь} насчет {альтернативного подхода|другого варианта}?",
    "{Можешь поделиться|Не подскажешь} {опытом|ссылками|источниками}?",
]

GRATITUDE_REPLY_TEMPLATES = [
    "{Спасибо за ответ|Благодарю за разъяснение}! {Очень помогло|Стало понятнее}.",
    "{Ценная информация|Полезные детали}! {Сохраню|Изучу подробнее}.",
    "{Отличное дополнение|Хорошее уточнение}! {Расширяет картину|Добавляет контекст}.",
]

# =============================================================================
# КОНТЕКСТНЫЕ РЕАКЦИИ
# =============================================================================

# Реакции для разных типов контента
CONTENT_REACTIONS = {
    "positive": ["👍", "👏", "🎉", "✨", "💯"],
    "love": ["❤️", "😍", "🥰", "💖", "😘"],
    "fire": ["🔥", "💥", "⚡", "🚀", "💪"],
    "smart": ["🧠", "💡", "🎯", "📚", "🤓"],
    "money": ["💰", "💎", "📈", "🤑", "💸"],
    "tech": ["⚙️", "💻", "🔧", "📱", "🖥️"],
}

# Ключевые слова для выбора контекстных реакций
REACTION_KEYWORDS = {
    "positive": ["отлично", "супер", "круто", "здорово", "классно", "прекрасно"],
    "love": ["любовь", "сердце", "обожаю", "нравится", "восторг", "милый"],
    "fire": ["огонь", "жара", "мощно", "взрыв", "бомба", "топ", "крутой"],
    "smart": ["умно", "мудро", "гениально", "интеллект", "знания", "образование"],
    "money": ["деньги", "доход", "прибыль", "инвестиции", "заработок", "бизнес"],
    "tech": ["технология", "код", "программа", "разработка", "IT", "софт"],
}

# =============================================================================
# ВРЕМЕННЫЕ НАСТРОЙКИ ДЛЯ РАЗНЫХ РЕЖИМОВ
# =============================================================================

# Консервативный режим (минимальная активность)
CONSERVATIVE_DELAYS = {
    "min_delay": 30 * 60,    # 30 минут
    "max_delay": 120 * 60,   # 2 часа
    "typing_min": 5,         # 5 секунд
    "typing_max": 12,        # 12 секунд
}

# Активный режим (умеренная активность)
ACTIVE_DELAYS = {
    "min_delay": 15 * 60,    # 15 минут
    "max_delay": 60 * 60,    # 1 час
    "typing_min": 3,         # 3 секунды
    "typing_max": 8,         # 8 секунд
}

# Агрессивный режим (высокая активность, больше риск)
AGGRESSIVE_DELAYS = {
    "min_delay": 5 * 60,     # 5 минут
    "max_delay": 30 * 60,    # 30 минут
    "typing_min": 2,         # 2 секунды
    "typing_max": 5,         # 5 секунд
}

# =============================================================================
# ЛИМИТЫ ДЛЯ РАЗНЫХ РЕЖИМОВ
# =============================================================================

CONSERVATIVE_LIMITS = {
    "comments": 3,
    "reactions": 10,
    "dialogues": 2,
}

ACTIVE_LIMITS = {
    "comments": 5,
    "reactions": 20,
    "dialogues": 3,
}

AGGRESSIVE_LIMITS = {
    "comments": 8,
    "reactions": 35,
    "dialogues": 5,
}

# =============================================================================
# НАСТРОЙКИ ПО ВРЕМЕНИ СУТОК
# =============================================================================

# Активность в разное время (коэффициенты)
TIME_ACTIVITY_MULTIPLIERS = {
    "morning": (6, 12, 1.2),    # 6-12: повышенная активность
    "day": (12, 18, 1.0),       # 12-18: обычная активность
    "evening": (18, 23, 1.5),   # 18-23: максимальная активность
    "night": (23, 6, 0.3),      # 23-6: минимальная активность
}

# =============================================================================
# ГРУППЫ ПО КАТЕГОРИЯМ
# =============================================================================

TARGET_GROUPS_BY_CATEGORY = {
    "tech": [
        "@python_developers",
        "@web_development",
        "@programming_chat",
    ],
    "business": [
        "@business_ideas",
        "@startup_community",
        "@marketing_tips",
    ],
    "lifestyle": [
        "@lifestyle_blog",
        "@travel_stories",
        "@food_lovers",
    ],
}

# =============================================================================
# ПРОДВИНУТЫЕ НАСТРОЙКИ БЕЗОПАСНОСТИ
# =============================================================================

ADVANCED_SECURITY = {
    "max_actions_per_hour": 3,           # Максимум действий в час
    "cooldown_after_error": 300,         # Пауза после ошибки (5 мин)
    "max_consecutive_actions": 2,        # Максимум подряд идущих действий
    "randomize_user_agent": True,        # Рандомизация User-Agent
    "simulate_human_breaks": True,       # Имитация перерывов
    "break_duration": (1800, 3600),     # Длительность перерывов (30-60 мин)
    "break_probability": 0.1,            # Вероятность перерыва (10%)
}

# =============================================================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# =============================================================================

DETAILED_LOG_CONFIG = {
    "level": "DEBUG",
    "format": "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S",
    "file_logging": True,
    "log_file": "farm.log",
    "max_log_size": 10 * 1024 * 1024,   # 10 MB
    "backup_count": 5,
}

# =============================================================================
# ПРИМЕР ИСПОЛЬЗОВАНИЯ В config.py
# =============================================================================

"""
# Для использования этих настроек, добавьте в config.py:

from example_config import (
    TECH_COMMENT_TEMPLATES,
    ACTIVE_DELAYS,
    ACTIVE_LIMITS,
    CONTENT_REACTIONS
)

# Замените стандартные настройки:
COMMENT_TEMPLATES = TECH_COMMENT_TEMPLATES
ACTION_DELAYS = ACTIVE_DELAYS
DAILY_LIMITS = ACTIVE_LIMITS

# Добавьте новые настройки:
CONTENT_REACTIONS = CONTENT_REACTIONS
"""
