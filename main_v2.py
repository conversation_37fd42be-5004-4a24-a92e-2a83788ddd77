"""
Главный оркестратор v2.0 для масштабируемой Telegram фермы
Интегрирует AI генерацию, веб-интерфейс и поддержку 100+ аккаунтов
"""

import asyncio
import csv
import logging
import random
import sys
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError
import socks

# Импорт наших модулей
from config import (
    ACCOUNTS_FILE, SESSIONS_DIR, TARGET_GROUPS, ACTION_DELAYS,
    LOG_CONFIG, PROXY_CONFIG, TELEGRAM_CONFIG, SECURITY_CONFIG
)
from modules.commenter import comment_random_post, get_comment_stats, leave_comment
from modules.dialogue import dialogue_manager, process_pending_reply, get_dialogue_stats, send_reply
from modules.reactor import react_to_random_post, get_reaction_stats, add_reaction_to_post
from modules.ai_generator import ai_generator
from modules.context_analyzer import context_analyzer
from modules.scalable_farm import (
    ScalableFarmManager, Task, TaskType, AccountStatus,
    PerformanceMonitor, LoadBalancer
)

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG["level"]),
    format=LOG_CONFIG["format"],
    datefmt=LOG_CONFIG["date_format"]
)
logger = logging.getLogger(__name__)


class EnhancedAccountManager:
    """Расширенный менеджер аккаунтов с поддержкой масштабирования"""
    
    def __init__(self, scalable_manager: ScalableFarmManager):
        self.accounts: List[Dict[str, str]] = []
        self.clients: Dict[str, TelegramClient] = {}
        self.scalable_manager = scalable_manager
        self.proxy_groups: Dict[str, List[str]] = {}
    
    def load_accounts(self) -> bool:
        """Загружает аккаунты из CSV файла"""
        try:
            if not Path(ACCOUNTS_FILE).exists():
                logger.error(f"Файл {ACCOUNTS_FILE} не найден")
                return False
            
            with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                self.accounts = list(reader)
            
            # Группируем аккаунты по прокси
            for account in self.accounts:
                proxy_key = f"{account.get('proxy_address', 'default')}:{account.get('proxy_port', '0')}"
                if proxy_key not in self.proxy_groups:
                    self.proxy_groups[proxy_key] = []
                self.proxy_groups[proxy_key].append(account['phone_number'])
            
            logger.info(f"Загружено {len(self.accounts)} аккаунтов в {len(self.proxy_groups)} группах прокси")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при загрузке аккаунтов: {e}")
            return False
    
    def create_client(self, account: Dict[str, str]) -> Optional[TelegramClient]:
        """Создает Telegram клиент для аккаунта"""
        try:
            phone = account['phone_number']
            api_id = int(account['api_id'])
            api_hash = account['api_hash']
            
            # Настройка прокси
            proxy = None
            if account.get('proxy_address') and account.get('proxy_port'):
                proxy = {
                    'proxy_type': socks.SOCKS5,
                    'addr': account['proxy_address'],
                    'port': int(account['proxy_port']),
                    'username': account.get('proxy_user'),
                    'password': account.get('proxy_pass'),
                    'rdns': True
                }
            
            # Путь к файлу сессии
            session_path = Path(SESSIONS_DIR) / f"{phone}.session"
            
            # Создаем клиент
            client = TelegramClient(
                str(session_path),
                api_id,
                api_hash,
                proxy=proxy,
                **TELEGRAM_CONFIG
            )
            
            return client
            
        except Exception as e:
            logger.error(f"Ошибка при создании клиента для {account.get('phone_number', 'unknown')}: {e}")
            return None
    
    async def initialize_clients(self) -> bool:
        """Инициализирует клиентов и регистрирует в масштабируемом менеджере"""
        if not self.accounts:
            logger.error("Нет загруженных аккаунтов")
            return False
        
        for account in self.accounts:
            phone = account['phone_number']
            proxy_key = f"{account.get('proxy_address', 'default')}:{account.get('proxy_port', '0')}"
            
            try:
                client = self.create_client(account)
                if not client:
                    continue
                
                if await self.authorize_client(client, phone):
                    self.clients[phone] = client
                    
                    # Регистрируем в масштабируемом менеджере
                    self.scalable_manager.register_account(phone, proxy_key)
                    
                    logger.info(f"Клиент {phone} готов к работе")
                else:
                    await client.disconnect()
                    
            except Exception as e:
                logger.error(f"Ошибка инициализации клиента {phone}: {e}")
        
        logger.info(f"Инициализировано {len(self.clients)} клиентов")
        return len(self.clients) > 0
    
    async def authorize_client(self, client: TelegramClient, phone: str) -> bool:
        """Авторизует клиент"""
        try:
            await client.connect()
            
            if not await client.is_user_authorized():
                logger.info(f"Требуется авторизация для {phone}")
                
                # Отправляем код
                await client.send_code_request(phone)
                
                # Запрашиваем код у пользователя
                code = input(f"Введите код для {phone}: ")
                
                try:
                    await client.sign_in(phone, code)
                except SessionPasswordNeededError:
                    password = input(f"Введите пароль для {phone}: ")
                    await client.sign_in(password=password)
            
            logger.info(f"Авторизация успешна для {phone}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка авторизации для {phone}: {e}")
            return False


class EnhancedFarmOrchestrator:
    """Расширенный оркестратор фермы с AI и масштабированием"""
    
    def __init__(self, max_connections: int = 100):
        self.scalable_manager = ScalableFarmManager(max_connections)
        self.account_manager = EnhancedAccountManager(self.scalable_manager)
        self.running = False
        self.stats = {
            "comments": 0,
            "reactions": 0,
            "dialogues": 0,
            "errors": 0,
            "ai_generations": 0
        }
        self.task_scheduler_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> bool:
        """Инициализирует ферму"""
        logger.info("Инициализация расширенной фермы...")
        
        # Загружаем аккаунты
        if not self.account_manager.load_accounts():
            return False
        
        # Инициализируем клиентов
        if not await self.account_manager.initialize_clients():
            return False
        
        # Запускаем масштабируемый менеджер
        await self.scalable_manager.start()
        
        logger.info("Расширенная ферма успешно инициализирована")
        return True
    
    async def create_comment_task(self, phone: str, group: str) -> Optional[Task]:
        """Создает задачу комментирования"""
        task = Task(
            task_id=str(uuid.uuid4()),
            task_type=TaskType.COMMENT,
            account_phone=phone,
            target_group=group,
            priority=2,
            data={"use_ai": True}
        )
        return task
    
    async def create_reaction_task(self, phone: str, group: str) -> Optional[Task]:
        """Создает задачу реакции"""
        task = Task(
            task_id=str(uuid.uuid4()),
            task_type=TaskType.REACTION,
            account_phone=phone,
            target_group=group,
            priority=1,
            data={}
        )
        return task
    
    async def create_dialogue_task(self, phone: str, group: str, original_message_id: int, original_text: str) -> Optional[Task]:
        """Создает задачу диалога"""
        task = Task(
            task_id=str(uuid.uuid4()),
            task_type=TaskType.DIALOGUE,
            account_phone=phone,
            target_group=group,
            priority=3,
            data={
                "original_message_id": original_message_id,
                "original_text": original_text,
                "use_ai": True
            }
        )
        return task
    
    async def schedule_random_tasks(self):
        """Планирует случайные задачи"""
        if not TARGET_GROUPS:
            return
        
        # Получаем доступные аккаунты
        available_phones = [
            phone for phone, account in self.scalable_manager.accounts.items()
            if account.status in [AccountStatus.IDLE, AccountStatus.ACTIVE]
        ]
        
        if not available_phones:
            return
        
        # Создаем случайные задачи
        num_tasks = min(random.randint(1, 5), len(available_phones))
        
        for _ in range(num_tasks):
            phone = random.choice(available_phones)
            group = random.choice(TARGET_GROUPS)
            action_type = random.choice(["comment", "reaction"])
            
            if action_type == "comment":
                task = await self.create_comment_task(phone, group)
            else:
                task = await self.create_reaction_task(phone, group)
            
            if task:
                self.scalable_manager.add_task(task)
                logger.debug(f"Запланирована задача {action_type} для {phone} в {group}")
    
    async def task_scheduler_loop(self):
        """Цикл планировщика задач"""
        while self.running:
            try:
                await self.schedule_random_tasks()
                
                # Обрабатываем диалоги
                ready_replies = dialogue_manager.get_ready_replies()
                for pending_reply in ready_replies:
                    task = await self.create_dialogue_task(
                        pending_reply.replier_phone,
                        pending_reply.group,
                        pending_reply.original_comment_id,
                        "Исходный комментарий"  # Здесь нужно получить текст
                    )
                    if task:
                        self.scalable_manager.add_task(task)
                
                # Случайная задержка
                delay = random.randint(
                    ACTION_DELAYS["min_delay"] // 4,
                    ACTION_DELAYS["min_delay"] // 2
                )
                await asyncio.sleep(delay)
                
            except Exception as e:
                logger.error(f"Ошибка в планировщике задач: {e}")
                await asyncio.sleep(60)
    
    def print_enhanced_stats(self):
        """Выводит расширенную статистику"""
        logger.info("=" * 60)
        logger.info("РАСШИРЕННАЯ СТАТИСТИКА ФЕРМЫ:")
        
        # Основная статистика
        logger.info(f"Комментарии: {self.stats['comments']}")
        logger.info(f"Реакции: {self.stats['reactions']}")
        logger.info(f"Диалоги: {self.stats['dialogues']}")
        logger.info(f"AI генерации: {self.stats['ai_generations']}")
        logger.info(f"Ошибки: {self.stats['errors']}")
        
        # Статистика масштабируемого менеджера
        scalable_stats = self.scalable_manager.get_stats()
        logger.info(f"Активных аккаунтов: {scalable_stats['active_accounts']}")
        logger.info(f"Размер очереди: {scalable_stats['queue_size']}")
        logger.info(f"Активных подключений: {scalable_stats['active_connections']}")
        
        # AI статистика
        ai_stats = ai_generator.get_stats()
        logger.info("AI провайдеры:")
        for provider, stats in ai_stats["providers"].items():
            status = "✅" if stats["available"] else "❌"
            logger.info(f"  {provider}: {status} ({stats['requests']} запросов)")
        
        # Системные метрики
        system_metrics = scalable_stats.get("system_metrics", {})
        logger.info(f"CPU: {system_metrics.get('cpu_percent', 0):.1f}%")
        logger.info(f"RAM: {system_metrics.get('memory_percent', 0):.1f}%")
        
        logger.info("=" * 60)
    
    async def start(self):
        """Запускает расширенную ферму"""
        if not await self.initialize():
            logger.error("Не удалось инициализировать ферму")
            return
        
        self.running = True
        logger.info("🚀 Расширенная ферма запущена!")
        
        # Запускаем планировщик задач
        self.task_scheduler_task = asyncio.create_task(self.task_scheduler_loop())
        
        try:
            cycle_count = 0
            while self.running:
                cycle_count += 1
                
                try:
                    # Выводим статистику каждые 20 циклов
                    if cycle_count % 20 == 0:
                        self.print_enhanced_stats()
                    
                    await asyncio.sleep(30)  # Основной цикл каждые 30 секунд
                
                except KeyboardInterrupt:
                    logger.info("Получен сигнал остановки")
                    break
                    
                except Exception as e:
                    logger.error(f"Ошибка в основном цикле: {e}")
                    self.stats["errors"] += 1
                    await asyncio.sleep(60)
        
        finally:
            await self.stop()
    
    async def stop(self):
        """Останавливает ферму"""
        logger.info("🛑 Остановка расширенной фермы...")
        self.running = False
        
        # Останавливаем планировщик
        if self.task_scheduler_task:
            self.task_scheduler_task.cancel()
        
        # Останавливаем масштабируемый менеджер
        await self.scalable_manager.stop()
        
        # Отключаем клиентов
        for phone, client in self.account_manager.clients.items():
            try:
                await client.disconnect()
                logger.info(f"Клиент {phone} отключен")
            except Exception as e:
                logger.error(f"Ошибка при отключении {phone}: {e}")
        
        # Финальная статистика
        self.print_enhanced_stats()
        logger.info("Расширенная ферма остановлена")


async def main():
    """Главная функция"""
    logger.info("Запуск расширенной Telegram фермы v2.0")
    
    # Проверяем наличие целевых групп
    if not TARGET_GROUPS:
        logger.error("Не указаны целевые группы в config.py")
        return
    
    # Создаем и запускаем оркестратор
    max_connections = 100  # Можно настроить через конфиг
    orchestrator = EnhancedFarmOrchestrator(max_connections)
    
    try:
        await orchestrator.start()
    except KeyboardInterrupt:
        logger.info("Программа прервана пользователем")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
    finally:
        logger.info("Завершение работы")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nПрограмма прервана пользователем")
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        sys.exit(1)
