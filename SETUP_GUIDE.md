# 🚀 Полное руководство по настройке Telegram-фермы

## 📋 Требования к системе

### Минимальные требования:
- **Python 3.9+** (рекомендуется 3.11+)
- **4 GB RAM** (для 2-3 аккаунтов)
- **8 GB RAM** (для 10+ аккаунтов)
- **Стабильное интернет-соединение**
- **SOCKS5 прокси** для каждого аккаунта

### Рекомендуемые требования:
- **Python 3.11+**
- **16 GB RAM** (для 50+ аккаунтов)
- **SSD диск** для быстрой работы с сессиями
- **Выделенный сервер** или VPS

## 🔧 Шаг 1: Подготовка окружения

### 1.1 Установка Python
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.11 python3.11-pip python3.11-venv

# CentOS/RHEL
sudo yum install python3.11 python3.11-pip

# macOS (через Homebrew)
brew install python@3.11

# Windows
# Скачайте с https://python.org
```

### 1.2 Создание виртуального окружения
```bash
# Переходим в папку проекта
cd /Users/<USER>/vibecode/ferma

# Создаем виртуальное окружение
python3.11 -m venv venv

# Активируем окружение
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

### 1.3 Установка зависимостей
```bash
# Обновляем pip
pip install --upgrade pip

# Устанавливаем зависимости
pip install -r requirements.txt

# Проверяем установку
python -c "import telethon; print('Telethon установлен успешно')"
```

## 🔑 Шаг 2: Получение API ключей Telegram

### 2.1 Регистрация приложения
1. Перейдите на https://my.telegram.org
2. Войдите с номером телефона
3. Перейдите в "API development tools"
4. Заполните форму создания приложения:
   - **App title**: "My Farm App"
   - **Short name**: "myfarm"
   - **Platform**: Desktop
   - **Description**: "Telegram automation"

### 2.2 Получение ключей
После создания приложения вы получите:
- **api_id** (число, например: ********)
- **api_hash** (строка, например: "abcd1234efgh5678...")

⚠️ **ВАЖНО**: Сохраните эти данные в безопасном месте!

## 🌐 Шаг 3: Настройка прокси

### 3.1 Получение SOCKS5 прокси
Рекомендуемые провайдеры:
- **ProxyLine** (https://proxyline.net)
- **Proxy6** (https://proxy6.net)
- **922S5** (для массовых операций)

### 3.2 Формат прокси
```
IP:PORT:USERNAME:PASSWORD
Пример: *************:1080:user123:pass456
```

### 3.3 Проверка прокси
```bash
# Установка curl для проверки
sudo apt install curl

# Проверка прокси
curl --socks5 user123:pass456@*************:1080 https://httpbin.org/ip
```

## 📱 Шаг 4: Настройка аккаунтов

### 4.1 Подготовка аккаунтов
Для безопасного тестирования используйте:
- **2-3 тестовых аккаунта** (не основные)
- **Разные прокси** для каждого аккаунта
- **Разные API ключи** (если возможно)

### 4.2 Редактирование accounts.csv
```csv
phone_number,api_id,api_hash,proxy_address,proxy_port,proxy_user,proxy_pass
+***********,********,your_api_hash_1,proxy1.com,1080,user1,pass1
+***********,********,your_api_hash_2,proxy2.com,1080,user2,pass2
+***********,********,your_api_hash_3,proxy3.com,1080,user3,pass3
```

### 4.3 Безопасные настройки для тестирования
В файле `config.py` установите:
```python
# БЕЗОПАСНЫЕ ЛИМИТЫ ДЛЯ ТЕСТИРОВАНИЯ
DAILY_LIMITS = {
    "comments": 1,      # ТОЛЬКО 1 комментарий в день
    "reactions": 2,     # ТОЛЬКО 2 реакции в день
    "dialogues": 0,     # ОТКЛЮЧАЕМ диалоги
}

# УВЕЛИЧЕННЫЕ ЗАДЕРЖКИ
ACTION_DELAYS = {
    "min_delay": 30 * 60,   # 30 минут между действиями
    "max_delay": 60 * 60,   # 60 минут максимум
}
```

## 🎯 Шаг 5: Настройка целевых групп

### 5.1 Выбор тестовых групп
Для безопасного тестирования:
- Создайте **собственную тестовую группу**
- Или используйте **небольшие публичные группы**
- **НЕ ТЕСТИРУЙТЕ** на больших каналах

### 5.2 Настройка в config.py
```python
TARGET_GROUPS = [
    "@your_test_group",     # Ваша тестовая группа
    # Добавьте другие группы после успешного тестирования
]
```

### 5.3 Создание тестовой группы
1. Создайте группу в Telegram
2. Добавьте тестовые аккаунты
3. Сделайте группу публичной с username
4. Добавьте username в TARGET_GROUPS

## 🚀 Шаг 6: Первый запуск

### 6.1 Проверка конфигурации
```bash
# Проверяем файлы
ls -la accounts.csv config.py

# Проверяем синтаксис Python
python -m py_compile config.py
python -m py_compile main.py
```

### 6.2 Тестовый запуск
```bash
# Активируем виртуальное окружение
source venv/bin/activate

# Запускаем базовую версию
python main.py
```

### 6.3 Процесс авторизации
При первом запуске для каждого аккаунта:
1. Система запросит **код подтверждения**
2. Проверьте Telegram на соответствующем номере
3. Введите **5-значный код**
4. Если включена 2FA - введите **пароль**

Пример:
```
Введите код для +***********: 12345
Введите пароль для +***********: your_2fa_password
```

## 🔍 Шаг 7: Проверка работоспособности

### 7.1 Мониторинг логов
Следите за выводом программы:
```
2024-01-15 10:30:15 - INFO - Ферма успешно инициализирована
2024-01-15 10:30:16 - INFO - 🚀 Ферма запущена!
2024-01-15 10:35:20 - INFO - ✅ Комментарий от +*********** в @test_group
```

### 7.2 Проверка в Telegram
1. Откройте целевую группу
2. Проверьте появление комментариев/реакций
3. Убедитесь что действия выглядят естественно

### 7.3 Статистика
Каждые 10 циклов выводится статистика:
```
==================================================
СТАТИСТИКА ФЕРМЫ:
Комментарии: 3
Реакции: 6
Диалоги: 0
Ошибки: 0
==================================================
```

## ⚠️ Важные моменты безопасности

### 🔒 Критически важно:
1. **Используйте уникальные прокси** для каждого аккаунта
2. **Не превышайте лимиты** - начинайте с минимальных значений
3. **Тестируйте на собственных группах** перед использованием на чужих
4. **Мониторьте аккаунты** на предмет ограничений

### 🚨 Признаки проблем:
- Ошибки "FloodWait" - увеличьте задержки
- Ошибки "ChatWriteForbidden" - проверьте права в группе
- Частые ошибки подключения - проверьте прокси

### 🛡️ Рекомендации:
- Начинайте с **1 комментария в день**
- Увеличивайте активность **постепенно**
- Используйте **разные шаблоны** текстов
- **Регулярно проверяйте** состояние аккаунтов

## 🔧 Устранение проблем

### Проблема: "Файл accounts.csv не найден"
```bash
# Проверьте наличие файла
ls -la accounts.csv

# Создайте файл если отсутствует
cp accounts.csv.example accounts.csv
```

### Проблема: "Invalid API credentials"
1. Проверьте правильность api_id и api_hash
2. Убедитесь что нет лишних пробелов
3. Проверьте что аккаунт не заблокирован

### Проблема: "Proxy connection failed"
```bash
# Проверьте прокси вручную
curl --socks5 user:pass@proxy:port https://httpbin.org/ip

# Проверьте формат в CSV файле
```

### Проблема: "FloodWait errors"
Увеличьте задержки в config.py:
```python
ACTION_DELAYS = {
    "min_delay": 60 * 60,   # 1 час
    "max_delay": 120 * 60,  # 2 часа
}
```

## ✅ Чек-лист готовности

Перед запуском убедитесь:
- [ ] Python 3.9+ установлен
- [ ] Виртуальное окружение создано и активировано
- [ ] Все зависимости установлены
- [ ] API ключи получены и настроены
- [ ] Прокси настроены и проверены
- [ ] accounts.csv заполнен корректно
- [ ] Целевые группы настроены
- [ ] Безопасные лимиты установлены
- [ ] Тестовая группа создана

## 🎉 Готово к запуску!

После выполнения всех шагов запустите:
```bash
python main.py
```

И следите за логами для контроля работы фермы.
