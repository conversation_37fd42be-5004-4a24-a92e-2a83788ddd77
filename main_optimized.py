"""
Оптимизированный оркестратор Telegram фермы
Устраняет блокирующие операции и оптимизирует производительность
"""

import asyncio
import csv
import logging
import random
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from pathlib import Path
from dataclasses import dataclass, field
from contextlib import asynccontextmanager

from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError

# Импорт наших модулей
from config import (
    ACCOUNTS_FILE, SESSIONS_DIR, TARGET_GROUPS, ACTION_DELAYS,
    LOG_CONFIG, PROXY_CONFIG, TELEGRAM_CONFIG, SECURITY_CONFIG, DAILY_LIMITS
)
from modules.connection_pool import connection_pool, PoolConfig
from modules.commenter import comment_random_post, get_comment_stats
from modules.dialogue import dialogue_manager, process_pending_reply, get_dialogue_stats
from modules.reactor import react_to_random_post, get_reaction_stats

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG["level"]),
    format=LOG_CONFIG["format"],
    datefmt=LOG_CONFIG["date_format"]
)
logger = logging.getLogger(__name__)


@dataclass
class TaskResult:
    """Результат выполнения задачи"""
    success: bool
    task_type: str
    account_id: str
    data: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    execution_time: float = 0.0


@dataclass
class FarmStats:
    """Статистика фермы"""
    comments: int = 0
    reactions: int = 0
    dialogues: int = 0
    errors: int = 0
    total_tasks: int = 0
    avg_execution_time: float = 0.0
    start_time: datetime = field(default_factory=datetime.now)


class OptimizedAccountManager:
    """Оптимизированный менеджер аккаунтов с пулом соединений"""
    
    def __init__(self):
        self.accounts: List[Dict[str, str]] = []
        self.active_accounts: Set[str] = set()
        self.account_last_action: Dict[str, datetime] = {}
        self.account_data: Dict[str, Dict[str, Any]] = {}
        
    def load_accounts(self) -> bool:
        """Загружает аккаунты из CSV файла"""
        try:
            if not Path(ACCOUNTS_FILE).exists():
                logger.error(f"Файл {ACCOUNTS_FILE} не найден")
                return False
            
            with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                self.accounts = list(reader)
            
            # Подготавливаем данные аккаунтов
            for account in self.accounts:
                phone = account['phone_number']
                self.account_data[phone] = account
                self.active_accounts.add(phone)
            
            logger.info(f"Загружено {len(self.accounts)} аккаунтов")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при загрузке аккаунтов: {e}")
            return False
    
    def get_available_accounts(self) -> List[str]:
        """Возвращает список доступных аккаунтов"""
        now = datetime.now()
        available = []
        
        for phone in self.active_accounts:
            # Проверяем не слишком ли недавно аккаунт выполнял действие
            last_action = self.account_last_action.get(phone)
            if last_action:
                time_since_action = (now - last_action).total_seconds()
                min_delay = ACTION_DELAYS["min_delay"]
                
                if time_since_action < min_delay:
                    continue
            
            available.append(phone)
        
        return available
    
    def update_last_action(self, phone: str):
        """Обновляет время последнего действия аккаунта"""
        self.account_last_action[phone] = datetime.now()


class OptimizedFarmOrchestrator:
    """Оптимизированный оркестратор фермы"""
    
    def __init__(self):
        self.account_manager = OptimizedAccountManager()
        self.running = False
        self.stats = FarmStats()
        
        # Очереди задач
        self.task_queue = asyncio.Queue(maxsize=1000)
        self.result_queue = asyncio.Queue(maxsize=1000)
        
        # Воркеры
        self.workers: List[asyncio.Task] = []
        self.num_workers = min(10, len(self.account_manager.accounts) if self.account_manager.accounts else 5)
        
        # Управление жизненным циклом
        self.shutdown_event = asyncio.Event()
        self.tasks_completed = asyncio.Event()
        
        # Настройка обработчиков сигналов
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """Настраивает обработчики сигналов для graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Получен сигнал {signum}, начинаем graceful shutdown...")
            asyncio.create_task(self.graceful_shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self) -> bool:
        """Инициализирует ферму"""
        logger.info("Инициализация оптимизированной фермы...")
        
        # Загружаем аккаунты
        if not self.account_manager.load_accounts():
            return False
        
        # Запускаем пул соединений
        pool_config = PoolConfig(
            max_connections=len(self.account_manager.accounts) * 2,
            min_connections=min(5, len(self.account_manager.accounts))
        )
        connection_pool.config = pool_config
        await connection_pool.start()
        
        logger.info("Оптимизированная ферма успешно инициализирована")
        return True
    
    async def start(self):
        """Запускает ферму"""
        if not await self.initialize():
            logger.error("Не удалось инициализировать ферму")
            return
        
        self.running = True
        self.stats.start_time = datetime.now()
        logger.info("🚀 Оптимизированная ферма запущена!")
        
        try:
            # Запускаем воркеров
            await self._start_workers()
            
            # Запускаем генератор задач
            task_generator = asyncio.create_task(self._task_generator())
            
            # Запускаем обработчик результатов
            result_processor = asyncio.create_task(self._result_processor())
            
            # Запускаем мониторинг
            monitor_task = asyncio.create_task(self._monitor_loop())
            
            # Ждем завершения
            await self.shutdown_event.wait()
            
            # Graceful shutdown
            await self._stop_workers()
            task_generator.cancel()
            result_processor.cancel()
            monitor_task.cancel()
            
        except Exception as e:
            logger.error(f"Критическая ошибка в оркестраторе: {e}")
        finally:
            await self.stop()
    
    async def _start_workers(self):
        """Запускает воркеров для обработки задач"""
        logger.info(f"Запуск {self.num_workers} воркеров...")
        
        for i in range(self.num_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"Запущено {len(self.workers)} воркеров")
    
    async def _stop_workers(self):
        """Останавливает воркеров"""
        logger.info("Остановка воркеров...")
        
        # Отменяем всех воркеров
        for worker in self.workers:
            worker.cancel()
        
        # Ждем завершения
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
        
        self.workers.clear()
        logger.info("Воркеры остановлены")
    
    async def _worker(self, worker_id: str):
        """Воркер для обработки задач"""
        logger.debug(f"Воркер {worker_id} запущен")
        
        while self.running and not self.shutdown_event.is_set():
            try:
                # Получаем задачу с таймаутом
                task = await asyncio.wait_for(
                    self.task_queue.get(), 
                    timeout=1.0
                )
                
                # Выполняем задачу
                result = await self._execute_task(task, worker_id)
                
                # Отправляем результат
                await self.result_queue.put(result)
                
                # Отмечаем задачу как выполненную
                self.task_queue.task_done()
                
            except asyncio.TimeoutError:
                # Таймаут получения задачи - это нормально
                continue
            except asyncio.CancelledError:
                logger.debug(f"Воркер {worker_id} отменен")
                break
            except Exception as e:
                logger.error(f"Ошибка в воркере {worker_id}: {e}")
                await asyncio.sleep(1)
        
        logger.debug(f"Воркер {worker_id} завершен")
    
    async def _execute_task(self, task: Dict[str, Any], worker_id: str) -> TaskResult:
        """Выполняет задачу"""
        start_time = datetime.now()
        task_type = task['type']
        account_id = task['account_id']
        
        try:
            account_data = self.account_manager.account_data[account_id]
            
            # Используем пул соединений
            async with connection_pool.get_connection(account_id, account_data) as client:
                result_data = None
                
                if task_type == "comment":
                    result_data = await comment_random_post(client, account_id, TARGET_GROUPS)
                elif task_type == "reaction":
                    result_data = await react_to_random_post(client, account_id, TARGET_GROUPS)
                elif task_type == "dialogue":
                    result_data = await self._process_dialogue_task(client, task)
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                if result_data:
                    self.account_manager.update_last_action(account_id)
                    return TaskResult(
                        success=True,
                        task_type=task_type,
                        account_id=account_id,
                        data=result_data,
                        execution_time=execution_time
                    )
                else:
                    return TaskResult(
                        success=False,
                        task_type=task_type,
                        account_id=account_id,
                        error="No result data",
                        execution_time=execution_time
                    )
        
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Ошибка выполнения задачи {task_type} для {account_id}: {e}")
            
            return TaskResult(
                success=False,
                task_type=task_type,
                account_id=account_id,
                error=str(e),
                execution_time=execution_time
            )
    
    async def _process_dialogue_task(self, client: TelegramClient, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Обрабатывает задачу диалога"""
        pending_reply = task.get('pending_reply')
        if pending_reply:
            return await process_pending_reply(
                {task['account_id']: client},
                pending_reply
            )
        return None
    
    async def _task_generator(self):
        """Генерирует задачи для выполнения"""
        logger.info("Генератор задач запущен")
        
        while self.running and not self.shutdown_event.is_set():
            try:
                # Получаем доступные аккаунты
                available_accounts = self.account_manager.get_available_accounts()
                
                if available_accounts:
                    # Выбираем случайный аккаунт
                    account_id = random.choice(available_accounts)
                    
                    # Выбираем случайное действие
                    actions = ["comment", "reaction"]
                    action = random.choice(actions)
                    
                    # Создаем задачу
                    task = {
                        'type': action,
                        'account_id': account_id,
                        'created_at': datetime.now()
                    }
                    
                    # Добавляем в очередь
                    await self.task_queue.put(task)
                    
                    logger.debug(f"Создана задача {action} для {account_id}")
                
                # Обрабатываем диалоги
                await self._generate_dialogue_tasks()
                
                # Задержка между генерацией задач
                delay = random.randint(
                    ACTION_DELAYS["min_delay"] // 8,
                    ACTION_DELAYS["min_delay"] // 4
                )
                await asyncio.sleep(delay)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в генераторе задач: {e}")
                await asyncio.sleep(60)
        
        logger.info("Генератор задач остановлен")
    
    async def _generate_dialogue_tasks(self):
        """Генерирует задачи для диалогов"""
        ready_replies = dialogue_manager.get_ready_replies()
        
        for pending_reply in ready_replies:
            task = {
                'type': 'dialogue',
                'account_id': pending_reply.replier_phone,
                'pending_reply': pending_reply,
                'created_at': datetime.now()
            }
            
            await self.task_queue.put(task)
    
    async def _result_processor(self):
        """Обрабатывает результаты выполнения задач"""
        logger.info("Обработчик результатов запущен")
        
        while self.running and not self.shutdown_event.is_set():
            try:
                # Получаем результат с таймаутом
                result = await asyncio.wait_for(
                    self.result_queue.get(),
                    timeout=1.0
                )
                
                # Обновляем статистику
                self._update_stats(result)
                
                # Логируем результат
                if result.success:
                    logger.info(f"✅ {result.task_type} от {result.account_id} выполнен за {result.execution_time:.2f}с")
                else:
                    logger.warning(f"❌ {result.task_type} от {result.account_id} не выполнен: {result.error}")
                
                # Отмечаем результат как обработанный
                self.result_queue.task_done()
                
            except asyncio.TimeoutError:
                continue
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в обработчике результатов: {e}")
        
        logger.info("Обработчик результатов остановлен")
    
    def _update_stats(self, result: TaskResult):
        """Обновляет статистику"""
        self.stats.total_tasks += 1
        
        if result.success:
            if result.task_type == "comment":
                self.stats.comments += 1
            elif result.task_type == "reaction":
                self.stats.reactions += 1
            elif result.task_type == "dialogue":
                self.stats.dialogues += 1
        else:
            self.stats.errors += 1
        
        # Обновляем среднее время выполнения
        if self.stats.avg_execution_time == 0:
            self.stats.avg_execution_time = result.execution_time
        else:
            self.stats.avg_execution_time = (
                self.stats.avg_execution_time + result.execution_time
            ) / 2
    
    async def _monitor_loop(self):
        """Мониторинг состояния фермы"""
        logger.info("Мониторинг запущен")
        
        while self.running and not self.shutdown_event.is_set():
            try:
                await asyncio.sleep(300)  # Каждые 5 минут
                
                # Выводим статистику
                self.print_stats()
                
                # Выводим статистику пула соединений
                pool_stats = connection_pool.get_stats()
                logger.info(f"Пул соединений: активных={pool_stats['current_active']}, "
                          f"создано={pool_stats['total_created']}, "
                          f"память={pool_stats['memory_usage']:.1f}MB")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в мониторинге: {e}")
        
        logger.info("Мониторинг остановлен")
    
    def print_stats(self):
        """Выводит статистику работы"""
        uptime = datetime.now() - self.stats.start_time
        
        logger.info("=" * 60)
        logger.info("СТАТИСТИКА ОПТИМИЗИРОВАННОЙ ФЕРМЫ:")
        logger.info(f"Время работы: {uptime}")
        logger.info(f"Комментарии: {self.stats.comments}")
        logger.info(f"Реакции: {self.stats.reactions}")
        logger.info(f"Диалоги: {self.stats.dialogues}")
        logger.info(f"Ошибки: {self.stats.errors}")
        logger.info(f"Всего задач: {self.stats.total_tasks}")
        logger.info(f"Среднее время выполнения: {self.stats.avg_execution_time:.2f}с")
        logger.info(f"Задач в очереди: {self.task_queue.qsize()}")
        logger.info(f"Результатов в очереди: {self.result_queue.qsize()}")
        logger.info("=" * 60)
    
    async def graceful_shutdown(self):
        """Корректное завершение работы"""
        logger.info("Начинаем graceful shutdown...")
        
        self.running = False
        
        # Ждем завершения текущих задач
        logger.info("Ожидание завершения текущих задач...")
        await asyncio.sleep(5)  # Даем время на завершение текущих операций
        
        # Сигнализируем о завершении
        self.shutdown_event.set()
        
        logger.info("Graceful shutdown завершен")
    
    async def stop(self):
        """Останавливает ферму"""
        logger.info("🛑 Остановка оптимизированной фермы...")
        
        # Останавливаем пул соединений
        await connection_pool.stop()
        
        # Финальная статистика
        self.print_stats()
        logger.info("Оптимизированная ферма остановлена")


async def main():
    """Главная функция"""
    logger.info("Запуск оптимизированной Telegram фермы")
    
    # Проверяем наличие целевых групп
    if not TARGET_GROUPS:
        logger.error("Не указаны целевые группы в config.py")
        return
    
    # Создаем и запускаем оркестратор
    orchestrator = OptimizedFarmOrchestrator()
    
    try:
        await orchestrator.start()
    except KeyboardInterrupt:
        logger.info("Программа прервана пользователем")
        await orchestrator.graceful_shutdown()
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        await orchestrator.graceful_shutdown()
    finally:
        logger.info("Завершение работы")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nПрограмма прервана пользователем")
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        sys.exit(1)
