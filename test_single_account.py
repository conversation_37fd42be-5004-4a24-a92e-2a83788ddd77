#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы фермы с одним аккаунтом
Безопасный режим с минимальными действиями
"""

import asyncio
import csv
import logging
import sys
from pathlib import Path

from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError
import socks

from config import (
    ACCOUNTS_FILE, SESSIONS_DIR, TARGET_GROUPS, 
    TELEGRAM_CONFIG, LOG_CONFIG
)
from modules.commenter import comment_random_post
from modules.reactor import react_to_random_post
from spintax import process_spintax

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_single_account():
    """Тестирует работу с одним аккаунтом"""
    
    print("🚀 Тестирование Telegram фермы с одним аккаунтом")
    print("=" * 50)
    
    # Проверяем наличие файла аккаунтов
    if not Path(ACCOUNTS_FILE).exists():
        print(f"❌ Файл {ACCOUNTS_FILE} не найден!")
        print("📝 Создайте файл accounts.csv с вашими данными:")
        print("phone_number,api_id,api_hash,proxy_address,proxy_port,proxy_user,proxy_pass")
        print("+YOUR_PHONE,YOUR_API_ID,YOUR_API_HASH,,,")
        return False
    
    # Загружаем аккаунт
    try:
        with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            accounts = list(reader)
        
        if not accounts:
            print("❌ Нет аккаунтов в файле!")
            return False
        
        account = accounts[0]  # Берем первый аккаунт
        phone = account['phone_number']
        
        print(f"📱 Тестируем аккаунт: {phone}")
        
    except Exception as e:
        print(f"❌ Ошибка чтения файла аккаунтов: {e}")
        return False
    
    # Проверяем целевые группы
    if not TARGET_GROUPS or TARGET_GROUPS == ["@test_group_for_farm"]:
        print("⚠️  ВНИМАНИЕ: Не настроены целевые группы!")
        print("📝 Отредактируйте config.py и укажите реальные группы в TARGET_GROUPS")
        print("Например: TARGET_GROUPS = ['@your_test_group']")
        
        response = input("Продолжить тестирование без групп? (y/N): ")
        if response.lower() != 'y':
            return False
    
    # Создаем клиент
    try:
        api_id = int(account['api_id'])
        api_hash = account['api_hash']
        
        # Настройка прокси (если есть)
        proxy = None
        if account.get('proxy_address') and account.get('proxy_port'):
            proxy = {
                'proxy_type': socks.SOCKS5,
                'addr': account['proxy_address'],
                'port': int(account['proxy_port']),
                'username': account.get('proxy_user'),
                'password': account.get('proxy_pass'),
                'rdns': True
            }
            print(f"🔒 Используется прокси: {account['proxy_address']}:{account['proxy_port']}")
        else:
            print("⚠️  Прокси не настроен (для тестов это нормально)")
        
        # Путь к сессии
        session_path = Path(SESSIONS_DIR) / f"{phone}.session"
        
        # Создаем клиент
        client = TelegramClient(
            str(session_path),
            api_id,
            api_hash,
            proxy=proxy,
            **TELEGRAM_CONFIG
        )
        
        print("🔗 Подключаемся к Telegram...")
        
    except Exception as e:
        print(f"❌ Ошибка создания клиента: {e}")
        return False
    
    # Авторизация
    try:
        await client.connect()
        
        if not await client.is_user_authorized():
            print("🔐 Требуется авторизация")
            
            # Отправляем код
            await client.send_code_request(phone)
            
            # Запрашиваем код
            code = input(f"📱 Введите код для {phone}: ")
            
            try:
                await client.sign_in(phone, code)
            except SessionPasswordNeededError:
                password = input("🔒 Введите пароль двухфакторной аутентификации: ")
                await client.sign_in(password=password)
        
        print("✅ Авторизация успешна!")
        
        # Получаем информацию о себе
        me = await client.get_me()
        print(f"👤 Вошли как: {me.first_name} {me.last_name or ''} (@{me.username or 'без username'})")
        
    except Exception as e:
        print(f"❌ Ошибка авторизации: {e}")
        await client.disconnect()
        return False
    
    # Тестируем базовые функции
    print("\n🧪 Тестирование базовых функций...")
    
    # Тест 1: Spintax
    print("1️⃣ Тестируем Spintax генерацию...")
    test_template = "{Привет|Добрый день}! {Тестовый|Пробный} комментарий."
    generated = process_spintax(test_template)
    print(f"   Сгенерировано: '{generated}'")
    
    # Тест 2: AI генерация (если настроена)
    try:
        from modules.ai_generator import ai_generator
        ai_stats = ai_generator.get_stats()
        active_providers = sum(1 for p in ai_stats["providers"].values() if p["available"])
        
        if active_providers > 0:
            print(f"2️⃣ AI провайдеры активны: {active_providers}")
            
            # Тестируем AI генерацию
            test_comment = await ai_generator.generate_comment(
                "Тестовый пост для проверки AI генерации",
                "test_group"
            )
            print(f"   AI комментарий: '{test_comment}'")
        else:
            print("2️⃣ AI провайдеры не настроены, используется Spintax")
            
    except Exception as e:
        print(f"2️⃣ AI недоступен: {e}")
    
    # Тест 3: Проверка групп (если настроены)
    if TARGET_GROUPS and TARGET_GROUPS != ["@test_group_for_farm"]:
        print("3️⃣ Проверяем доступ к целевым группам...")
        
        for group in TARGET_GROUPS[:2]:  # Проверяем первые 2 группы
            try:
                entity = await client.get_entity(group)
                print(f"   ✅ Группа {group}: {entity.title}")
                
                # Проверяем права
                permissions = await client.get_permissions(entity)
                can_send = permissions.send_messages
                print(f"      Права на отправку: {'✅' if can_send else '❌'}")
                
            except Exception as e:
                print(f"   ❌ Группа {group}: {e}")
    
    # Интерактивное тестирование
    print("\n🎮 Интерактивное тестирование")
    print("Выберите действие:")
    print("1. Отправить тестовый комментарий")
    print("2. Поставить тестовую реакцию")
    print("3. Только проверить подключение")
    print("4. Выйти")
    
    choice = input("Ваш выбор (1-4): ")
    
    if choice == "1" and TARGET_GROUPS and TARGET_GROUPS != ["@test_group_for_farm"]:
        print("📝 Тестируем отправку комментария...")
        try:
            result = await comment_random_post(client, phone, TARGET_GROUPS)
            if result:
                print(f"✅ Комментарий отправлен в {result['group']}")
                print(f"   Текст: '{result['comment_text']}'")
            else:
                print("❌ Не удалось отправить комментарий")
        except Exception as e:
            print(f"❌ Ошибка отправки комментария: {e}")
    
    elif choice == "2" and TARGET_GROUPS and TARGET_GROUPS != ["@test_group_for_farm"]:
        print("👍 Тестируем постановку реакции...")
        try:
            result = await react_to_random_post(client, phone, TARGET_GROUPS)
            if result:
                print(f"✅ Реакция поставлена в {result['group']}")
                print(f"   Реакция: {result['reaction']}")
            else:
                print("❌ Не удалось поставить реакцию")
        except Exception as e:
            print(f"❌ Ошибка постановки реакции: {e}")
    
    elif choice == "3":
        print("✅ Подключение работает корректно!")
    
    elif choice == "4":
        print("👋 Выход из тестирования")
    
    else:
        print("⚠️  Для полного тестирования настройте TARGET_GROUPS в config.py")
    
    # Отключаемся
    await client.disconnect()
    print("\n✅ Тестирование завершено!")
    print("📊 Результат: Аккаунт готов к работе")
    
    return True


async def main():
    """Главная функция"""
    try:
        success = await test_single_account()
        
        if success:
            print("\n🎉 Тест пройден успешно!")
            print("\n📋 Следующие шаги:")
            print("1. Настройте TARGET_GROUPS в config.py")
            print("2. Получите AI API ключи (опционально)")
            print("3. Запустите полную ферму: python main_v2.py")
            print("4. Или веб-интерфейс: python api_server.py")
        else:
            print("\n❌ Тест не пройден. Проверьте настройки.")
            
    except KeyboardInterrupt:
        print("\n👋 Тестирование прервано пользователем")
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🔧 Telegram Farm - Тестирование одного аккаунта")
    print("Убедитесь что:")
    print("✅ Установлены зависимости: pip install -r requirements.txt")
    print("✅ Настроен accounts.csv с вашими данными")
    print("✅ Указаны целевые группы в config.py")
    print()
    
    asyncio.run(main())
