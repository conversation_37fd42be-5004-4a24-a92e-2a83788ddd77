#!/usr/bin/env python3
"""
Тестовый файл для проверки работы модулей фермы
Запускает базовые тесты без подключения к Telegram
"""

import asyncio
import sys
from datetime import datetime

# Тестирование Spintax
def test_spintax():
    """Тестирует модуль Spintax"""
    print("🧪 Тестирование модуля Spintax...")
    
    from spintax import process_spintax, validate_spintax, get_spintax_variants_count
    
    # Тест 1: Базовая обработка
    template = "{Привет|Добрый день}! {Отличный|Хороший} пост!"
    result = process_spintax(template)
    print(f"✅ Базовая обработка: '{result}'")
    
    # Тест 2: Валидация
    valid = validate_spintax(template)
    print(f"✅ Валидация корректного шаблона: {valid}")
    
    invalid = validate_spintax("{Привет|Добрый день! Отличный пост!")
    print(f"✅ Валидация некорректного шаблона: {not invalid}")
    
    # Тест 3: Подсчет вариантов
    count = get_spintax_variants_count(template)
    print(f"✅ Количество вариантов: {count} (ожидается 4)")
    
    print("✅ Модуль Spintax работает корректно\n")


def test_config():
    """Тестирует конфигурацию"""
    print("🧪 Тестирование конфигурации...")
    
    import config
    
    # Проверяем основные настройки
    assert hasattr(config, 'TARGET_GROUPS'), "Отсутствует TARGET_GROUPS"
    assert hasattr(config, 'DAILY_LIMITS'), "Отсутствует DAILY_LIMITS"
    assert hasattr(config, 'COMMENT_TEMPLATES'), "Отсутствует COMMENT_TEMPLATES"
    assert hasattr(config, 'AVAILABLE_REACTIONS'), "Отсутствует AVAILABLE_REACTIONS"
    
    print(f"✅ Целевых групп: {len(config.TARGET_GROUPS)}")
    print(f"✅ Шаблонов комментариев: {len(config.COMMENT_TEMPLATES)}")
    print(f"✅ Доступных реакций: {len(config.AVAILABLE_REACTIONS)}")
    print(f"✅ Лимит комментариев в день: {config.DAILY_LIMITS['comments']}")
    
    print("✅ Конфигурация загружена корректно\n")


def test_trackers():
    """Тестирует трекеры лимитов"""
    print("🧪 Тестирование трекеров лимитов...")
    
    from modules.commenter import CommentTracker
    from modules.reactor import ReactionTracker
    
    # Тест комментариев
    comment_tracker = CommentTracker()
    test_phone = "+***********"
    
    # Проверяем начальное состояние
    assert comment_tracker.can_comment(test_phone), "Должен разрешать комментарии"
    
    # Добавляем комментарии до лимита
    for i in range(5):  # DAILY_LIMITS['comments'] = 5
        comment_tracker.add_comment(test_phone)
    
    # Проверяем что лимит достигнут
    assert not comment_tracker.can_comment(test_phone), "Должен запрещать комментарии после лимита"
    
    print("✅ Трекер комментариев работает корректно")
    
    # Тест реакций
    reaction_tracker = ReactionTracker()
    
    assert reaction_tracker.can_react(test_phone), "Должен разрешать реакции"
    
    # Добавляем реакции
    for i in range(3):
        reaction_tracker.add_reaction(test_phone)
    
    stats = reaction_tracker.get_stats(test_phone)
    assert stats['reactions'] == 3, f"Ожидается 3 реакции, получено {stats['reactions']}"
    
    print("✅ Трекер реакций работает корректно")
    print("✅ Все трекеры работают корректно\n")


def test_dialogue_manager():
    """Тестирует менеджер диалогов"""
    print("🧪 Тестирование менеджера диалогов...")
    
    from modules.dialogue import DialogueManager
    
    manager = DialogueManager()
    test_phone = "+***********"
    
    # Проверяем начальное состояние
    assert manager.can_start_dialogue(test_phone), "Должен разрешать диалоги"
    
    # Тестируем планирование ответа
    comment_info = {
        "phone": "+79001234568",
        "group": "@test_group",
        "comment_id": 123,
        "comment_text": "Тестовый комментарий"
    }
    
    available_phones = ["+***********", "+79001234568", "+79001234569"]
    
    pending_reply = manager.schedule_reply(comment_info, available_phones)
    
    assert pending_reply is not None, "Должен создать запланированный ответ"
    assert pending_reply.replier_phone != comment_info["phone"], "Отвечающий не должен быть автором"
    
    print("✅ Планирование ответов работает корректно")
    print("✅ Менеджер диалогов работает корректно\n")


def test_reaction_selection():
    """Тестирует выбор реакций"""
    print("🧪 Тестирование выбора реакций...")
    
    from modules.reactor import select_reaction
    
    # Тест позитивных слов
    positive_text = "Отличный пост, супер!"
    reaction = select_reaction(positive_text)
    print(f"✅ Позитивный текст: '{positive_text}' -> {reaction}")
    
    # Тест эмоциональных слов
    emotional_text = "Обожаю это, сердце радуется!"
    reaction = select_reaction(emotional_text)
    print(f"✅ Эмоциональный текст: '{emotional_text}' -> {reaction}")
    
    # Тест огненных слов
    fire_text = "Это просто огонь! Мощно!"
    reaction = select_reaction(fire_text)
    print(f"✅ Огненный текст: '{fire_text}' -> {reaction}")
    
    # Тест нейтрального текста
    neutral_text = "Обычный пост без особых слов"
    reaction = select_reaction(neutral_text)
    print(f"✅ Нейтральный текст: '{neutral_text}' -> {reaction}")
    
    print("✅ Выбор реакций работает корректно\n")


def test_account_manager():
    """Тестирует менеджер аккаунтов (без реального подключения)"""
    print("🧪 Тестирование менеджера аккаунтов...")
    
    # Импортируем только класс, не создаем подключения
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    
    from main import AccountManager
    
    manager = AccountManager()
    
    # Проверяем что методы существуют
    assert hasattr(manager, 'load_accounts'), "Отсутствует метод load_accounts"
    assert hasattr(manager, 'get_available_accounts'), "Отсутствует метод get_available_accounts"
    assert hasattr(manager, 'update_last_action'), "Отсутствует метод update_last_action"
    
    # Тестируем обновление времени действия
    test_phone = "+***********"
    manager.active_accounts = [test_phone]
    
    # Изначально аккаунт доступен
    available = manager.get_available_accounts()
    assert test_phone in available, "Аккаунт должен быть доступен"
    
    # После действия аккаунт недоступен некоторое время
    manager.update_last_action(test_phone)
    available = manager.get_available_accounts()
    assert test_phone not in available, "Аккаунт не должен быть доступен сразу после действия"
    
    print("✅ Менеджер аккаунтов работает корректно\n")


def main():
    """Запускает все тесты"""
    print("🚀 Запуск тестов модулей Telegram фермы\n")
    
    try:
        test_spintax()
        test_config()
        test_trackers()
        test_dialogue_manager()
        test_reaction_selection()
        test_account_manager()
        
        print("🎉 Все тесты пройдены успешно!")
        print("✅ Модули готовы к работе")
        
    except Exception as e:
        print(f"❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
