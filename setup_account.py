#!/usr/bin/env python3
"""
Безопасная настройка аккаунта для Telegram фермы
Помогает получить API ключи и настроить первое подключение
"""

import asyncio
import csv
import logging
from pathlib import Path
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class AccountSetup:
    """Помощник для настройки аккаунта"""
    
    def __init__(self):
        self.phone = "+*************"
        self.api_id = None
        self.api_hash = None
        self.client = None
    
    def get_api_credentials(self):
        """Получает API ключи от пользователя"""
        print("🔑 ПОЛУЧЕНИЕ API КЛЮЧЕЙ TELEGRAM")
        print("=" * 50)
        print("1. Перейдите на https://my.telegram.org")
        print(f"2. Войдите с номером телефона: {self.phone}")
        print("3. Перейдите в 'API development tools'")
        print("4. Создайте приложение:")
        print("   - App title: MyFarm")
        print("   - Short name: myfarm")
        print("   - Platform: Desktop")
        print("   - Description: Telegram automation")
        print()
        
        while True:
            try:
                api_id = input("Введите ваш api_id: ").strip()
                self.api_id = int(api_id)
                break
            except ValueError:
                print("❌ api_id должен быть числом. Попробуйте еще раз.")
        
        self.api_hash = input("Введите ваш api_hash: ").strip()
        
        print(f"\n✅ API ключи получены:")
        print(f"   api_id: {self.api_id}")
        print(f"   api_hash: {self.api_hash}")
        
        return True
    
    def update_accounts_file(self, use_proxy=False):
        """Обновляет файл accounts.csv"""
        try:
            if use_proxy:
                # С прокси (если работает)
                data = [
                    ["phone_number", "api_id", "api_hash", "proxy_address", "proxy_port", "proxy_user", "proxy_pass"],
                    [self.phone, self.api_id, self.api_hash, "*************", "64389", "Y1gC3qxS", "Wu4tmdT2"]
                ]
            else:
                # Без прокси (для первого тестирования)
                data = [
                    ["phone_number", "api_id", "api_hash", "proxy_address", "proxy_port", "proxy_user", "proxy_pass"],
                    [self.phone, self.api_id, self.api_hash, "", "", "", ""]
                ]
            
            with open("accounts.csv", "w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerows(data)
            
            print("✅ Файл accounts.csv обновлен")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка обновления файла: {e}")
            return False
    
    async def test_connection(self):
        """Тестирует подключение к Telegram"""
        print("\n📱 ТЕСТИРОВАНИЕ ПОДКЛЮЧЕНИЯ К TELEGRAM")
        print("=" * 50)
        
        try:
            # Создаем папку для сессий
            Path("sessions").mkdir(exist_ok=True)
            
            # Создаем клиент БЕЗ прокси для первого теста
            session_path = f"sessions/{self.phone}.session"
            self.client = TelegramClient(
                session_path,
                self.api_id,
                self.api_hash,
                device_model="Desktop",
                system_version="Windows 10",
                app_version="4.8.4",
                lang_code="ru",
                system_lang_code="ru-RU"
            )
            
            print("🔗 Подключение к Telegram...")
            await self.client.connect()
            
            if not await self.client.is_user_authorized():
                print("🔐 Требуется авторизация")
                print(f"📱 Отправляем код на {self.phone}...")
                
                await self.client.send_code_request(self.phone)
                
                # Запрашиваем код
                code = input("📨 Введите код из Telegram: ").strip()
                
                try:
                    await self.client.sign_in(self.phone, code)
                except SessionPasswordNeededError:
                    password = input("🔒 Введите пароль 2FA: ").strip()
                    await self.client.sign_in(password=password)
            
            # Получаем информацию о себе
            me = await self.client.get_me()
            print(f"\n✅ УСПЕШНАЯ АВТОРИЗАЦИЯ!")
            print(f"👤 Имя: {me.first_name}")
            print(f"📞 Телефон: {me.phone}")
            print(f"🆔 ID: {me.id}")
            if me.username:
                print(f"👤 Username: @{me.username}")
            
            return True
            
        except PhoneCodeInvalidError:
            print("❌ Неверный код. Попробуйте еще раз.")
            return False
        except Exception as e:
            print(f"❌ Ошибка подключения: {e}")
            return False
        finally:
            if self.client:
                await self.client.disconnect()
    
    async def run_setup(self):
        """Запускает полную настройку"""
        print("🚀 НАСТРОЙКА TELEGRAM АККАУНТА")
        print("=" * 60)
        print(f"📞 Номер телефона: {self.phone}")
        print("🌐 Прокси: *************:64389 (будет протестирован)")
        print()
        
        # Шаг 1: Получаем API ключи
        if not self.get_api_credentials():
            return False
        
        # Шаг 2: Обновляем файл (сначала без прокси)
        print("\n📝 Обновление конфигурации...")
        if not self.update_accounts_file(use_proxy=False):
            return False
        
        # Шаг 3: Тестируем подключение
        success = await self.test_connection()
        
        if success:
            print("\n🎉 НАСТРОЙКА ЗАВЕРШЕНА УСПЕШНО!")
            print("=" * 60)
            print("✅ Аккаунт настроен и авторизован")
            print("✅ Сессия сохранена")
            print("✅ Готов к использованию")
            print()
            print("📋 СЛЕДУЮЩИЕ ШАГИ:")
            print("1. Проверьте работу прокси с провайдером")
            print("2. Обновите accounts.csv с рабочим прокси")
            print("3. Запустите безопасное тестирование:")
            print("   python safe_personal_test.py")
            print()
            print("⚠️  ВАЖНО: Используйте только безопасные лимиты!")
            
            # Обновляем файл с прокси для будущего использования
            print("\n🔄 Создание конфигурации с прокси для будущего использования...")
            self.update_accounts_file(use_proxy=True)
            print("💡 Когда прокси заработает, используйте эту конфигурацию")
            
        else:
            print("\n❌ НАСТРОЙКА НЕ ЗАВЕРШЕНА")
            print("Проверьте API ключи и попробуйте еще раз")
        
        return success

async def main():
    """Главная функция"""
    setup = AccountSetup()
    await setup.run_setup()

if __name__ == "__main__":
    asyncio.run(main())
