"""
Главный оркестратор для управления фермой Telegram-аккаунтов
Координирует работу всех модулей и аккаунтов
"""

import asyncio
import csv
import logging
import random
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError
import socks

# Импорт наших модулей
from config import (
    ACCOUNTS_FILE, SESSIONS_DIR, TARGET_GROUPS, ACTION_DELAYS,
    LOG_CONFIG, PROXY_CONFIG, TELEGRAM_CONFIG, SECURITY_CONFIG
)
from modules.commenter import comment_random_post, get_comment_stats
from modules.dialogue import dialogue_manager, process_pending_reply, get_dialogue_stats
from modules.reactor import react_to_random_post, get_reaction_stats

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG["level"]),
    format=LOG_CONFIG["format"],
    datefmt=LOG_CONFIG["date_format"]
)
logger = logging.getLogger(__name__)


class AccountManager:
    """Управляет аккаунтами и их клиентами"""
    
    def __init__(self):
        self.accounts: List[Dict[str, str]] = []
        self.clients: Dict[str, TelegramClient] = {}
        self.active_accounts: List[str] = []
        self.account_last_action: Dict[str, datetime] = {}
    
    def load_accounts(self) -> bool:
        """Загружает аккаунты из CSV файла"""
        try:
            if not Path(ACCOUNTS_FILE).exists():
                logger.error(f"Файл {ACCOUNTS_FILE} не найден")
                return False
            
            with open(ACCOUNTS_FILE, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                self.accounts = list(reader)
            
            logger.info(f"Загружено {len(self.accounts)} аккаунтов")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при загрузке аккаунтов: {e}")
            return False
    
    def create_client(self, account: Dict[str, str]) -> Optional[TelegramClient]:
        """Создает Telegram клиент для аккаунта"""
        try:
            phone = account['phone_number']
            api_id = int(account['api_id'])
            api_hash = account['api_hash']
            
            # Настройка прокси
            proxy = None
            if account.get('proxy_address') and account.get('proxy_port'):
                proxy = {
                    'proxy_type': socks.SOCKS5,
                    'addr': account['proxy_address'],
                    'port': int(account['proxy_port']),
                    'username': account.get('proxy_user'),
                    'password': account.get('proxy_pass'),
                    'rdns': True
                }
            
            # Путь к файлу сессии
            session_path = Path(SESSIONS_DIR) / f"{phone}.session"
            
            # Создаем клиент
            client = TelegramClient(
                str(session_path),
                api_id,
                api_hash,
                proxy=proxy,
                **TELEGRAM_CONFIG
            )
            
            return client
            
        except Exception as e:
            logger.error(f"Ошибка при создании клиента для {account.get('phone_number', 'unknown')}: {e}")
            return None
    
    async def authorize_client(self, client: TelegramClient, phone: str) -> bool:
        """Авторизует клиент"""
        try:
            await client.connect()
            
            if not await client.is_user_authorized():
                logger.info(f"Требуется авторизация для {phone}")
                
                # Отправляем код
                await client.send_code_request(phone)
                
                # Запрашиваем код у пользователя
                code = input(f"Введите код для {phone}: ")
                
                try:
                    await client.sign_in(phone, code)
                except SessionPasswordNeededError:
                    password = input(f"Введите пароль для {phone}: ")
                    await client.sign_in(password=password)
            
            logger.info(f"Авторизация успешна для {phone}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка авторизации для {phone}: {e}")
            return False
    
    async def initialize_clients(self) -> bool:
        """Инициализирует всех клиентов"""
        if not self.accounts:
            logger.error("Нет загруженных аккаунтов")
            return False
        
        for account in self.accounts:
            phone = account['phone_number']
            
            try:
                client = self.create_client(account)
                if not client:
                    continue
                
                if await self.authorize_client(client, phone):
                    self.clients[phone] = client
                    self.active_accounts.append(phone)
                    logger.info(f"Клиент {phone} готов к работе")
                else:
                    await client.disconnect()
                    
            except Exception as e:
                logger.error(f"Ошибка инициализации клиента {phone}: {e}")
        
        logger.info(f"Инициализировано {len(self.clients)} клиентов")
        return len(self.clients) > 0
    
    async def disconnect_all(self):
        """Отключает всех клиентов"""
        for phone, client in self.clients.items():
            try:
                await client.disconnect()
                logger.info(f"Клиент {phone} отключен")
            except Exception as e:
                logger.error(f"Ошибка при отключении {phone}: {e}")
    
    def get_available_accounts(self) -> List[str]:
        """Возвращает список доступных аккаунтов"""
        now = datetime.now()
        available = []
        
        for phone in self.active_accounts:
            # Проверяем не слишком ли недавно аккаунт выполнял действие
            last_action = self.account_last_action.get(phone)
            if last_action:
                time_since_action = (now - last_action).total_seconds()
                min_delay = ACTION_DELAYS["min_delay"]
                
                if time_since_action < min_delay:
                    continue
            
            available.append(phone)
        
        return available
    
    def update_last_action(self, phone: str):
        """Обновляет время последнего действия аккаунта"""
        self.account_last_action[phone] = datetime.now()


class FarmOrchestrator:
    """Главный оркестратор фермы"""
    
    def __init__(self):
        self.account_manager = AccountManager()
        self.running = False
        self.stats = {
            "comments": 0,
            "reactions": 0,
            "dialogues": 0,
            "errors": 0
        }
    
    async def initialize(self) -> bool:
        """Инициализирует ферму"""
        logger.info("Инициализация фермы...")
        
        # Загружаем аккаунты
        if not self.account_manager.load_accounts():
            return False
        
        # Инициализируем клиентов
        if not await self.account_manager.initialize_clients():
            return False
        
        logger.info("Ферма успешно инициализирована")
        return True

    async def perform_random_action(self) -> bool:
        """Выполняет случайное действие случайным аккаунтом"""
        available_accounts = self.account_manager.get_available_accounts()

        if not available_accounts:
            logger.debug("Нет доступных аккаунтов для действий")
            return False

        # Выбираем случайный аккаунт
        phone = random.choice(available_accounts)
        client = self.account_manager.clients[phone]

        # Выбираем случайное действие (комментарий, реакция)
        actions = ["comment", "reaction"]
        action = random.choice(actions)

        try:
            if action == "comment":
                result = await comment_random_post(client, phone, TARGET_GROUPS)
                if result:
                    self.stats["comments"] += 1
                    self.account_manager.update_last_action(phone)

                    # Планируем возможный диалог
                    if random.random() < 0.3:  # 30% шанс диалога
                        dialogue_manager.schedule_reply(
                            result,
                            self.account_manager.active_accounts
                        )

                    logger.info(f"✅ Комментарий от {phone} в {result['group']}")
                    return True

            elif action == "reaction":
                result = await react_to_random_post(client, phone, TARGET_GROUPS)
                if result:
                    self.stats["reactions"] += 1
                    self.account_manager.update_last_action(phone)
                    logger.info(f"✅ Реакция от {phone} в {result['group']}")
                    return True

        except Exception as e:
            logger.error(f"Ошибка при выполнении действия {action} для {phone}: {e}")
            self.stats["errors"] += 1

        return False

    async def process_dialogues(self):
        """Обрабатывает запланированные диалоги"""
        ready_replies = dialogue_manager.get_ready_replies()

        for pending_reply in ready_replies:
            try:
                result = await process_pending_reply(
                    self.account_manager.clients,
                    pending_reply
                )

                if result:
                    self.stats["dialogues"] += 1
                    self.account_manager.update_last_action(result["replier_phone"])
                    logger.info(
                        f"✅ Диалог: {result['replier_phone']} ответил "
                        f"{result['original_phone']} в {result['group']}"
                    )

            except Exception as e:
                logger.error(f"Ошибка при обработке диалога: {e}")
                self.stats["errors"] += 1

    def print_stats(self):
        """Выводит статистику работы"""
        logger.info("=" * 50)
        logger.info("СТАТИСТИКА ФЕРМЫ:")
        logger.info(f"Комментарии: {self.stats['comments']}")
        logger.info(f"Реакции: {self.stats['reactions']}")
        logger.info(f"Диалоги: {self.stats['dialogues']}")
        logger.info(f"Ошибки: {self.stats['errors']}")
        logger.info("=" * 50)

        # Статистика по аккаунтам
        for phone in self.account_manager.active_accounts:
            comment_stats = get_comment_stats(phone)
            reaction_stats = get_reaction_stats(phone)
            dialogue_stats = get_dialogue_stats(phone)

            logger.info(
                f"[{phone}] Комментарии: {comment_stats.get('comments', 0)}, "
                f"Реакции: {reaction_stats.get('reactions', 0)}, "
                f"Диалоги: {dialogue_stats.get('dialogues_today', 0)}"
            )

    async def run_cycle(self):
        """Выполняет один цикл работы фермы"""
        # Обрабатываем диалоги
        await self.process_dialogues()

        # Выполняем случайное действие
        await self.perform_random_action()

        # Случайная задержка между циклами
        delay = random.randint(
            ACTION_DELAYS["min_delay"] // 4,  # Четверть минимальной задержки
            ACTION_DELAYS["min_delay"] // 2   # Половина минимальной задержки
        )

        logger.debug(f"Ожидание {delay} секунд до следующего цикла")
        await asyncio.sleep(delay)

    async def start(self):
        """Запускает ферму"""
        if not await self.initialize():
            logger.error("Не удалось инициализировать ферму")
            return

        self.running = True
        logger.info("🚀 Ферма запущена!")

        try:
            cycle_count = 0
            while self.running:
                cycle_count += 1

                try:
                    await self.run_cycle()

                    # Выводим статистику каждые 10 циклов
                    if cycle_count % 10 == 0:
                        self.print_stats()

                except KeyboardInterrupt:
                    logger.info("Получен сигнал остановки")
                    break

                except Exception as e:
                    logger.error(f"Ошибка в цикле фермы: {e}")
                    self.stats["errors"] += 1
                    await asyncio.sleep(60)  # Пауза при ошибке

        finally:
            await self.stop()

    async def stop(self):
        """Останавливает ферму"""
        logger.info("🛑 Остановка фермы...")
        self.running = False

        # Отключаем всех клиентов
        await self.account_manager.disconnect_all()

        # Финальная статистика
        self.print_stats()
        logger.info("Ферма остановлена")


async def main():
    """Главная функция"""
    logger.info("Запуск Telegram фермы")

    # Проверяем наличие целевых групп
    if not TARGET_GROUPS:
        logger.error("Не указаны целевые группы в config.py")
        return

    # Создаем и запускаем оркестратор
    orchestrator = FarmOrchestrator()

    try:
        await orchestrator.start()
    except KeyboardInterrupt:
        logger.info("Программа прервана пользователем")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
    finally:
        logger.info("Завершение работы")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nПрограмма прервана пользователем")
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        sys.exit(1)
