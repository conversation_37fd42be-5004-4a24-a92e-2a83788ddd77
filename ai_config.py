"""
Конфигурация AI провайдеров для Telegram фермы
Содержит настройки для различных бесплатных и платных API
"""

import os
from typing import Dict, Any

# =============================================================================
# НАСТРОЙКИ AI ПРОВАЙДЕРОВ
# =============================================================================

# DeepSeek (бесплатный API)
DEEPSEEK_CONFIG = {
    "api_key": os.getenv("DEEPSEEK_API_KEY", ""),
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat",
    "max_tokens": 100,
    "temperature": 0.7,
    "enabled": True,
    "rate_limit": 60,  # запросов в минуту
    "description": "DeepSeek - бесплатный API с хорошим качеством генерации"
}

# Groq (бесплатный API)
GROQ_CONFIG = {
    "api_key": os.getenv("GROQ_API_KEY", ""),
    "base_url": "https://api.groq.com/openai/v1",
    "model": "llama3-8b-8192",
    "max_tokens": 100,
    "temperature": 0.8,
    "enabled": True,
    "rate_limit": 30,  # запросов в минуту
    "description": "Groq - быстрый бесплатный API на базе LLaMA"
}

# OpenAI (платный)
OPENAI_CONFIG = {
    "api_key": os.getenv("OPENAI_API_KEY", ""),
    "base_url": "https://api.openai.com/v1",
    "model": "gpt-3.5-turbo",
    "max_tokens": 100,
    "temperature": 0.7,
    "enabled": False,  # По умолчанию отключен
    "rate_limit": 3500,  # запросов в минуту
    "description": "OpenAI GPT-3.5 - платный, но качественный API"
}

# Ollama (локальный)
OLLAMA_CONFIG = {
    "api_key": "",  # Не требуется для локального
    "base_url": "http://localhost:11434/v1",
    "model": "llama3.2:3b",
    "max_tokens": 100,
    "temperature": 0.8,
    "enabled": False,  # Требует локальной установки
    "rate_limit": 1000,  # Зависит от железа
    "description": "Ollama - локальная установка LLM моделей"
}

# Hugging Face (бесплатный с ограничениями)
HUGGINGFACE_CONFIG = {
    "api_key": os.getenv("HUGGINGFACE_API_KEY", ""),
    "base_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium",
    "model": "microsoft/DialoGPT-medium",
    "max_tokens": 100,
    "temperature": 0.8,
    "enabled": False,
    "rate_limit": 1000,  # запросов в час
    "description": "Hugging Face - бесплатный API с различными моделями"
}

# =============================================================================
# ПРОМПТЫ ДЛЯ РАЗНЫХ ТИПОВ КОНТЕНТА
# =============================================================================

COMMENT_PROMPTS = {
    "tech": """Ты пишешь короткий комментарий к техническому посту в IT группе. 
Комментарий должен быть профессиональным, но дружелюбным.
Максимум 40 слов, на русском языке, без эмодзи.

Пост: "{post_text}"

Комментарий:""",

    "business": """Ты пишешь комментарий к бизнес-посту. 
Будь конструктивным и показывай заинтересованность в теме.
Максимум 40 слов, на русском языке, без эмодзи.

Пост: "{post_text}"

Комментарий:""",

    "lifestyle": """Ты пишешь дружелюбный комментарий к посту о стиле жизни.
Будь позитивным и поддерживающим.
Максимум 40 слов, на русском языке, без эмодзи.

Пост: "{post_text}"

Комментарий:""",

    "general": """Ты пишешь естественный комментарий к посту в Telegram группе.
Будь дружелюбным и человечным.
Максимум 40 слов, на русском языке, без эмодзи.

Пост: "{post_text}"

Комментарий:"""
}

REPLY_PROMPTS = {
    "agreement": """Ты отвечаешь на комментарий, выражая согласие.
Будь дружелюбным и конструктивным.
Максимум 30 слов, на русском языке, без эмодзи.

Исходный комментарий: "{original_comment}"

Ответ:""",

    "question": """Ты отвечаешь на комментарий, задавая уточняющий вопрос или выражая интерес.
Будь любознательным и вежливым.
Максимум 30 слов, на русском языке, без эмодзи.

Исходный комментарий: "{original_comment}"

Ответ:""",

    "support": """Ты отвечаешь на комментарий, выражая поддержку или благодарность.
Будь искренним и теплым.
Максимум 30 слов, на русском языке, без эмодзи.

Исходный комментарий: "{original_comment}"

Ответ:""",

    "general": """Ты отвечаешь на комментарий в Telegram группе.
Будь естественным и дружелюбным.
Максимум 30 слов, на русском языке, без эмодзи.

Исходный комментарий: "{original_comment}"

Ответ:"""
}

# =============================================================================
# НАСТРОЙКИ FALLBACK
# =============================================================================

FALLBACK_CONFIG = {
    "enabled": True,
    "use_spintax": True,
    "use_templates": True,
    "min_ai_success_rate": 0.7,  # Минимальный процент успешных AI запросов
    "fallback_after_errors": 3,  # Переключение на fallback после N ошибок подряд
}

# =============================================================================
# НАСТРОЙКИ КАЧЕСТВА
# =============================================================================

QUALITY_CONFIG = {
    "min_length": 5,      # Минимальная длина сгенерированного текста
    "max_length": 200,    # Максимальная длина
    "forbidden_words": [  # Запрещенные слова/фразы
        "как ИИ", "как искусственный интеллект", "я не могу", 
        "извините", "к сожалению", "нейросеть", "модель"
    ],
    "required_language": "ru",  # Требуемый язык
    "filter_emoji": True,       # Удалять эмодзи
    "filter_urls": True,        # Удалять URL
}

# =============================================================================
# РОТАЦИЯ ПРОВАЙДЕРОВ
# =============================================================================

ROTATION_CONFIG = {
    "enabled": True,
    "strategy": "round_robin",  # round_robin, random, load_based
    "weights": {  # Веса для провайдеров (чем больше, тем чаще используется)
        "deepseek": 3,
        "groq": 2,
        "openai": 1,
        "ollama": 1,
        "huggingface": 1
    },
    "cooldown_after_error": 300,  # Секунд до повторного использования после ошибки
}

# =============================================================================
# МОНИТОРИНГ AI
# =============================================================================

MONITORING_CONFIG = {
    "track_response_time": True,
    "track_success_rate": True,
    "track_quality_score": True,
    "log_failed_requests": True,
    "alert_on_high_error_rate": True,
    "error_rate_threshold": 0.5,  # 50% ошибок
}

# =============================================================================
# ФУНКЦИИ КОНФИГУРАЦИИ
# =============================================================================

def get_active_providers() -> Dict[str, Dict[str, Any]]:
    """Возвращает список активных провайдеров"""
    providers = {
        "deepseek": DEEPSEEK_CONFIG,
        "groq": GROQ_CONFIG,
        "openai": OPENAI_CONFIG,
        "ollama": OLLAMA_CONFIG,
        "huggingface": HUGGINGFACE_CONFIG
    }
    
    return {
        name: config for name, config in providers.items()
        if config["enabled"] and config.get("api_key", "") != ""
    }

def get_prompt_for_content(content_type: str, prompt_type: str = "comment") -> str:
    """Возвращает промпт для определенного типа контента"""
    if prompt_type == "comment":
        return COMMENT_PROMPTS.get(content_type, COMMENT_PROMPTS["general"])
    elif prompt_type == "reply":
        return REPLY_PROMPTS.get(content_type, REPLY_PROMPTS["general"])
    else:
        return COMMENT_PROMPTS["general"]

def validate_ai_response(text: str) -> bool:
    """Проверяет качество AI ответа"""
    if not text or len(text) < QUALITY_CONFIG["min_length"]:
        return False
    
    if len(text) > QUALITY_CONFIG["max_length"]:
        return False
    
    text_lower = text.lower()
    for forbidden in QUALITY_CONFIG["forbidden_words"]:
        if forbidden in text_lower:
            return False
    
    return True

def clean_ai_response(text: str) -> str:
    """Очищает AI ответ от нежелательных элементов"""
    import re
    
    # Удаляем эмодзи если настроено
    if QUALITY_CONFIG["filter_emoji"]:
        text = re.sub(r'[^\w\s\.,!?;:\-()]+', '', text)
    
    # Удаляем URL если настроено
    if QUALITY_CONFIG["filter_urls"]:
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    
    # Убираем лишние пробелы
    text = ' '.join(text.split())
    
    return text.strip()

# =============================================================================
# ИНСТРУКЦИИ ПО НАСТРОЙКЕ
# =============================================================================

SETUP_INSTRUCTIONS = """
🔧 НАСТРОЙКА AI ПРОВАЙДЕРОВ:

1. DeepSeek (рекомендуется):
   - Регистрация: https://platform.deepseek.com/
   - Получите API ключ в разделе API Keys
   - Установите переменную: export DEEPSEEK_API_KEY="your-key"

2. Groq (быстрый):
   - Регистрация: https://console.groq.com/
   - Получите API ключ
   - Установите переменную: export GROQ_API_KEY="your-key"

3. OpenAI (платный):
   - Регистрация: https://platform.openai.com/
   - Пополните баланс
   - Установите переменную: export OPENAI_API_KEY="your-key"

4. Ollama (локальный):
   - Установите Ollama: https://ollama.ai/
   - Запустите: ollama run llama3.2:3b
   - Включите в конфиге: OLLAMA_CONFIG["enabled"] = True

📝 РЕКОМЕНДАЦИИ:
- Начните с DeepSeek - он бесплатный и качественный
- Настройте несколько провайдеров для надежности
- Включите fallback на Spintax для стабильности
- Мониторьте качество генерации через веб-интерфейс
"""

if __name__ == "__main__":
    print(SETUP_INSTRUCTIONS)
    print("\n🔍 АКТИВНЫЕ ПРОВАЙДЕРЫ:")
    active = get_active_providers()
    if active:
        for name, config in active.items():
            print(f"✅ {name}: {config['description']}")
    else:
        print("❌ Нет активных провайдеров. Настройте API ключи.")
