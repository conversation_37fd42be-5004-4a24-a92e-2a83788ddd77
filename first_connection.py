#!/usr/bin/env python3
"""
🛡️ БЕЗОПАСНОЕ ПЕРВОЕ ПОДКЛЮЧЕНИЕ К TELEGRAM
Настройка и авторизация аккаунта +6283140647775
"""

import asyncio
import csv
import logging
from pathlib import Path
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError
import socks

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - 🛡️ SAFE_SETUP - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class SafeFirstConnection:
    """Безопасное первое подключение"""
    
    def __init__(self):
        # Данные аккаунта
        self.phone = "+6283140647775"
        self.api_id = 24976030
        self.api_hash = "320690fc08a869f3e270f82a31954fa7"
        
        # Данные прокси SOCKS5
        self.proxy_config = {
            'proxy_type': socks.SOCKS5,
            'addr': '176.103.95.80',
            'port': 64389,
            'username': 'Y1gC3qxS',
            'password': 'Wu4tmdT2',
            'rdns': True
        }
        
        self.client = None
    
    async def test_connection_without_proxy(self):
        """Тестирует подключение БЕЗ прокси (для первой проверки)"""
        print("🔍 Тест 1: Подключение БЕЗ прокси...")
        
        try:
            # Создаем папку для сессий
            Path("sessions").mkdir(exist_ok=True)
            
            # Клиент без прокси
            session_path = f"sessions/{self.phone}_no_proxy.session"
            client = TelegramClient(
                session_path,
                self.api_id,
                self.api_hash,
                device_model="Desktop",
                system_version="Windows 10",
                app_version="4.8.4",
                lang_code="en",
                system_lang_code="en-US"
            )
            
            await client.connect()
            
            # Проверяем подключение
            if await client.is_connected():
                print("✅ Подключение к Telegram успешно (без прокси)")
                await client.disconnect()
                return True
            else:
                print("❌ Не удалось подключиться к Telegram")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка подключения без прокси: {e}")
            return False
    
    async def test_connection_with_proxy(self):
        """Тестирует подключение С прокси"""
        print("🔍 Тест 2: Подключение С прокси SOCKS5...")
        
        try:
            # Клиент с прокси
            session_path = f"sessions/{self.phone}_with_proxy.session"
            client = TelegramClient(
                session_path,
                self.api_id,
                self.api_hash,
                proxy=self.proxy_config,
                device_model="Desktop",
                system_version="Windows 10",
                app_version="4.8.4",
                lang_code="en",
                system_lang_code="en-US"
            )
            
            print(f"🌐 Подключение через прокси {self.proxy_config['addr']}:{self.proxy_config['port']}...")
            await client.connect()
            
            if await client.is_connected():
                print("✅ Подключение к Telegram успешно (с прокси)")
                await client.disconnect()
                return True
            else:
                print("❌ Не удалось подключиться через прокси")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка подключения с прокси: {e}")
            print("💡 Возможные причины:")
            print("   - Прокси недоступен")
            print("   - Неверные данные прокси")
            print("   - Прокси заблокирован")
            return False
    
    async def authorize_account(self, use_proxy=True):
        """Авторизует аккаунт"""
        print(f"\n🔐 АВТОРИЗАЦИЯ АККАУНТА {self.phone}")
        print("=" * 50)
        
        try:
            # Выбираем конфигурацию
            if use_proxy:
                session_path = f"sessions/{self.phone}.session"
                proxy = self.proxy_config
                print("🌐 Используем прокси для авторизации")
            else:
                session_path = f"sessions/{self.phone}_no_proxy.session"
                proxy = None
                print("🔗 Авторизация без прокси")
            
            # Создаем клиент
            self.client = TelegramClient(
                session_path,
                self.api_id,
                self.api_hash,
                proxy=proxy,
                device_model="Desktop",
                system_version="Windows 10",
                app_version="4.8.4",
                lang_code="en",
                system_lang_code="en-US"
            )
            
            await self.client.connect()
            
            if not await self.client.is_user_authorized():
                print("📱 Отправляем код авторизации...")
                await self.client.send_code_request(self.phone)
                
                # Запрашиваем код у пользователя
                print(f"\n📨 Код отправлен на {self.phone}")
                code = input("Введите код из Telegram: ").strip()
                
                try:
                    await self.client.sign_in(self.phone, code)
                except SessionPasswordNeededError:
                    password = input("🔒 Введите пароль 2FA: ").strip()
                    await self.client.sign_in(password=password)
            
            # Получаем информацию о пользователе
            me = await self.client.get_me()
            
            print(f"\n🎉 АВТОРИЗАЦИЯ УСПЕШНА!")
            print(f"👤 Имя: {me.first_name}")
            if me.last_name:
                print(f"👤 Фамилия: {me.last_name}")
            print(f"📞 Телефон: {me.phone}")
            print(f"🆔 ID: {me.id}")
            if me.username:
                print(f"👤 Username: @{me.username}")
            
            return True
            
        except FloodWaitError as e:
            print(f"❌ FloodWait: нужно подождать {e.seconds} секунд")
            return False
        except PhoneCodeInvalidError:
            print("❌ Неверный код. Попробуйте еще раз.")
            return False
        except Exception as e:
            print(f"❌ Ошибка авторизации: {e}")
            return False
        finally:
            if self.client:
                await self.client.disconnect()
    
    async def run_setup(self):
        """Запускает полную настройку"""
        print("🚀 БЕЗОПАСНАЯ НАСТРОЙКА TELEGRAM АККАУНТА")
        print("=" * 60)
        print(f"📞 Номер: {self.phone}")
        print(f"🔑 API ID: {self.api_id}")
        print(f"🔑 API Hash: {self.api_hash[:10]}...")
        print(f"🌐 Прокси: {self.proxy_config['addr']}:{self.proxy_config['port']}")
        print()
        
        # Тест 1: Подключение без прокси
        no_proxy_works = await self.test_connection_without_proxy()
        
        # Тест 2: Подключение с прокси
        proxy_works = await self.test_connection_with_proxy()
        
        print("\n📊 РЕЗУЛЬТАТЫ ТЕСТОВ:")
        print(f"   Без прокси: {'✅ Работает' if no_proxy_works else '❌ Не работает'}")
        print(f"   С прокси: {'✅ Работает' if proxy_works else '❌ Не работает'}")
        
        # Выбираем метод авторизации
        if proxy_works:
            print("\n🌐 Используем прокси для авторизации (рекомендуется)")
            success = await self.authorize_account(use_proxy=True)
        elif no_proxy_works:
            print("\n⚠️ Прокси не работает, используем прямое подключение")
            print("💡 Рекомендуется решить проблему с прокси перед использованием фермы")
            success = await self.authorize_account(use_proxy=False)
        else:
            print("\n❌ НИ ОДИН МЕТОД ПОДКЛЮЧЕНИЯ НЕ РАБОТАЕТ")
            print("🔧 Проверьте:")
            print("   - Интернет соединение")
            print("   - API ключи")
            print("   - Данные прокси")
            return False
        
        if success:
            print("\n🎉 НАСТРОЙКА ЗАВЕРШЕНА УСПЕШНО!")
            print("=" * 60)
            print("✅ Аккаунт авторизован")
            print("✅ Сессия сохранена")
            print("✅ Готов к безопасному тестированию")
            print()
            print("📋 СЛЕДУЮЩИЕ ШАГИ:")
            print("1. Запустите безопасное тестирование:")
            print("   python safe_personal_test.py")
            print()
            print("2. Или запустите оптимизированную ферму:")
            print("   python main_optimized.py")
            print()
            print("⚠️ ВАЖНО:")
            print("   - Используйте только безопасные лимиты")
            print("   - Тестируйте на собственных группах")
            if not proxy_works:
                print("   - Решите проблему с прокси для продакшена")
        
        return success

async def main():
    """Главная функция"""
    setup = SafeFirstConnection()
    await setup.run_setup()

if __name__ == "__main__":
    asyncio.run(main())
