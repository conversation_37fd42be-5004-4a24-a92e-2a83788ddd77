# 🚀 Быстрый старт Telegram фермы v2.0

## 📋 Что нужно для начала

1. **Python 3.9+** установлен
2. **API ключи Telegram** (api_id, api_hash) с https://my.telegram.org
3. **SOCKS5 прокси** для каждого аккаунта
4. **AI API ключи** (опционально, но рекомендуется)

## ⚡ Быстрая установка

### 1. Клонирование и установка зависимостей
```bash
# Переходим в папку проекта
cd telegram_farm

# Устанавливаем зависимости
pip install -r requirements.txt
```

### 2. Настройка аккаунтов
Отредактируйте файл `accounts.csv`:
```csv
phone_number,api_id,api_hash,proxy_address,proxy_port,proxy_user,proxy_pass
+***********,********,your_api_hash,proxy1.com,1080,user1,pass1
+***********,********,your_api_hash,proxy2.com,1080,user2,pass2
```

### 3. Настройка целевых групп
В файле `config.py` укажите группы:
```python
TARGET_GROUPS = [
    "@your_target_group1",
    "@your_target_group2",
]
```

### 4. Настройка AI (рекомендуется)
```bash
# DeepSeek (бесплатный)
export DEEPSEEK_API_KEY="your-deepseek-key"

# Groq (бесплатный)
export GROQ_API_KEY="your-groq-key"
```

## 🎯 Варианты запуска

### Вариант 1: Базовая ферма (оригинал)
```bash
python main.py
```

### Вариант 2: Расширенная ферма с AI
```bash
python main_v2.py
```

### Вариант 3: С веб-интерфейсом
```bash
# Терминал 1: API сервер
python api_server.py

# Терминал 2: Ферма
python main_v2.py

# Браузер: http://localhost:8000/dashboard
```

## 🧪 Тестирование

### Проверка базовых модулей
```bash
python test_modules.py
```

### Проверка новых возможностей
```bash
python test_enhanced_features.py
```

### Проверка AI конфигурации
```bash
python ai_config.py
```

## 🔧 Настройка AI провайдеров

### DeepSeek (рекомендуется)
1. Регистрация: https://platform.deepseek.com/
2. Получите API ключ в разделе API Keys
3. `export DEEPSEEK_API_KEY="your-key"`

### Groq (быстрый)
1. Регистрация: https://console.groq.com/
2. Получите API ключ
3. `export GROQ_API_KEY="your-key"`

### OpenAI (платный)
1. Регистрация: https://platform.openai.com/
2. Пополните баланс
3. `export OPENAI_API_KEY="your-key"`

## 📊 Мониторинг

### Веб-интерфейс
- URL: http://localhost:8000/dashboard
- Real-time статистика
- Управление фермой
- Просмотр логов

### API эндпоинты
- `GET /api/status` - статус фермы
- `GET /api/stats` - статистика
- `GET /api/accounts` - аккаунты
- `POST /api/farm/start` - запуск
- `POST /api/farm/stop` - остановка

## ⚙️ Основные настройки

### Лимиты (config.py)
```python
DAILY_LIMITS = {
    "comments": 5,      # Комментариев в день
    "reactions": 20,    # Реакций в день
    "dialogues": 3,     # Диалогов в день
}
```

### Задержки
```python
ACTION_DELAYS = {
    "min_delay": 15 * 60,   # 15 минут
    "max_delay": 60 * 60,   # 60 минут
}
```

### AI настройки (ai_config.py)
```python
FALLBACK_CONFIG = {
    "enabled": True,              # Включить fallback
    "use_spintax": True,         # Использовать Spintax
    "min_ai_success_rate": 0.7,  # Минимальный % успеха AI
}
```

## 🚨 Важные моменты

### Безопасность
- ✅ Используйте уникальные прокси для каждого аккаунта
- ✅ Не превышайте лимиты действий
- ✅ Тестируйте на небольших группах
- ✅ Мониторьте логи на ошибки

### Производительность
- 📈 Базовая версия: до 10 аккаунтов
- 📈 Расширенная версия: до 100+ аккаунтов
- 📈 Веб-интерфейс: real-time мониторинг

### AI генерация
- 🤖 Fallback на Spintax при ошибках AI
- 🤖 Контекстная генерация по тематике постов
- 🤖 Валидация качества сгенерированных текстов

## 🔍 Диагностика проблем

### Ошибка "No module named 'telethon'"
```bash
pip install telethon
```

### Ошибка "Invalid API Key"
- Проверьте правильность API ключей
- Убедитесь что переменные окружения установлены

### Ошибка "FloodWait"
- Увеличьте задержки в config.py
- Уменьшите количество действий

### Нет активных AI провайдеров
```bash
# Проверьте конфигурацию
python ai_config.py

# Установите API ключи
export DEEPSEEK_API_KEY="your-key"
```

## 📞 Поддержка

### Логи
- Все действия логируются в консоль
- Уровень логирования настраивается в config.py

### Мониторинг
- Веб-интерфейс показывает состояние в реальном времени
- API предоставляет детальную статистику

### Тестирование
- Запускайте тесты перед использованием
- Проверяйте работу на тестовых группах

## 🎉 Готово!

После выполнения всех шагов у вас будет:
- ✅ Работающая ферма Telegram аккаунтов
- ✅ AI генерация естественных сообщений
- ✅ Веб-интерфейс для управления
- ✅ Масштабируемая архитектура
- ✅ Система мониторинга

**Telegram ферма v2.0 готова к работе!** 🚀

---

*При возникновении проблем проверьте логи и убедитесь что все зависимости установлены*
