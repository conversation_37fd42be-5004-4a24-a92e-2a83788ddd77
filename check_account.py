#!/usr/bin/env python3
"""
🔍 ПРОВЕРКА СОСТОЯНИЯ АККАУНТА
Проверяет состояние аккаунта и сессии без запроса кода
"""

import asyncio
import logging
import os
from pathlib import Path
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError
import socks

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_account():
    """Проверяет состояние аккаунта"""
    
    # Данные аккаунта
    phone = "+*************"
    api_id = ********
    api_hash = "320690fc08a869f3e270f82a31954fa7"
    
    # Прокси SOCKS5
    proxy_config = {
        'proxy_type': socks.SOCKS5,
        'addr': '176.103.95.80',
        'port': 64389,
        'username': 'Y1gC3qxS',
        'password': 'Wu4tmdT2',
        'rdns': True
    }
    
    print("🔍 ПРОВЕРКА СОСТОЯНИЯ АККАУНТА")
    print("=" * 40)
    print(f"📞 Номер: {phone}")
    print(f"🌐 Прокси: {proxy_config['addr']}:{proxy_config['port']}")
    print()
    
    # Проверяем наличие сессий
    print("📁 Проверка файлов сессий:")
    sessions_dir = Path("sessions")
    session_files = list(sessions_dir.glob(f"*{phone}*.session"))
    
    if not session_files:
        print("❌ Сессии не найдены")
        return False
    
    for session_file in session_files:
        print(f"   ✅ {session_file}")
    
    # Проверяем основную сессию
    main_session = f"sessions/{phone}.session"
    if os.path.exists(main_session):
        print(f"✅ Основная сессия существует: {main_session}")
        session_to_use = main_session
    else:
        # Используем первую найденную сессию
        session_to_use = str(session_files[0])
        print(f"⚠️ Основная сессия не найдена, используем: {session_to_use}")
    
    # Пробуем подключиться без авторизации
    print("\n🔗 Проверка подключения (без запроса кода)...")
    
    try:
        # Создаем клиент
        client = TelegramClient(
            session_to_use.replace(".session", ""),
            api_id,
            api_hash,
            proxy=proxy_config
        )
        
        await client.connect()
        
        # Проверяем авторизацию
        if await client.is_user_authorized():
            print("✅ Аккаунт авторизован")
            
            # Получаем информацию о пользователе
            me = await client.get_me()
            print(f"\n👤 ИНФОРМАЦИЯ ОБ АККАУНТЕ:")
            print(f"   Имя: {me.first_name}")
            if me.last_name:
                print(f"   Фамилия: {me.last_name}")
            print(f"   Телефон: {me.phone}")
            print(f"   ID: {me.id}")
            if me.username:
                print(f"   Username: @{me.username}")
            
            # Проверяем диалоги
            print("\n💬 Проверка доступа к диалогам...")
            try:
                dialogs = await client.get_dialogs(limit=5)
                print(f"✅ Доступ к диалогам есть, получено {len(dialogs)} диалогов")
                
                # Показываем первые диалоги
                for i, dialog in enumerate(dialogs[:3], 1):
                    print(f"   {i}. {dialog.name}")
            except Exception as e:
                print(f"❌ Ошибка доступа к диалогам: {e}")
            
            # Проверяем прокси
            print("\n🌐 Проверка прокси...")
            try:
                # Простой запрос через прокси
                await client.get_me()
                print("✅ Прокси работает")
            except Exception as e:
                print(f"❌ Ошибка прокси: {e}")
            
            result = True
        else:
            print("❌ Аккаунт НЕ авторизован, требуется ввод кода")
            print("💡 Запустите python quick_auth.py для авторизации")
            result = False
        
        await client.disconnect()
        return result
        
    except Exception as e:
        print(f"❌ Ошибка проверки: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(check_account())
