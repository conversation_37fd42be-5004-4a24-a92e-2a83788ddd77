#!/usr/bin/env python3
"""
🛡️ БЕЗОПАСНОЕ тестирование Telegram фермы для ЛИЧНОГО аккаунта
Максимальная осторожность для предотвращения блокировки
"""

import asyncio
import csv
import logging
import sys
import os
from pathlib import Path
from datetime import datetime

from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, FloodWaitError
import socks

from safe_test_config import (
    SAFE_DAILY_LIMITS, SAFE_ACTION_DELAYS, SAFE_REACTIONS,
    SAFE_COMMENT_TEMPLATES, FLOOD_PROTECTION, SAFETY_MONITORING,
    TEST_GROUPS
)

# Настройка безопасного логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - 🛡️ SAFE_TEST - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler('safe_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SafePersonalAccountTester:
    """Безопасный тестер для личного аккаунта"""
    
    def __init__(self):
        self.client = None
        self.phone = None
        self.actions_count = 0
        self.errors_count = 0
        self.start_time = datetime.now()
    
    async def safety_check(self):
        """Проверка безопасности перед началом"""
        print("🛡️ ПРОВЕРКА БЕЗОПАСНОСТИ")
        print("=" * 50)
        
        # Проверка 1: Файл аккаунтов
        if not Path("accounts.csv").exists():
            print("❌ Файл accounts.csv не найден!")
            return False
        
        # Проверка 2: Тестовые группы
        if not TEST_GROUPS or TEST_GROUPS == []:
            print("❌ КРИТИЧНО: Не настроены тестовые группы!")
            print("📝 Отредактируйте safe_test_config.py и укажите TEST_GROUPS")
            print("   Пример: TEST_GROUPS = ['@your_test_group']")
            return False
        
        # Проверка 3: Лимиты безопасности
        if SAFE_DAILY_LIMITS["comments"] > 1:
            print("⚠️  ВНИМАНИЕ: Лимит комментариев больше 1!")
        
        if SAFE_ACTION_DELAYS["min_delay"] < 30 * 60:
            print("⚠️  ВНИМАНИЕ: Задержки меньше 30 минут!")
        
        print("✅ Безопасные лимиты:")
        print(f"   Комментарии: {SAFE_DAILY_LIMITS['comments']}/день")
        print(f"   Реакции: {SAFE_DAILY_LIMITS['reactions']}/день")
        print(f"   Задержки: {SAFE_ACTION_DELAYS['min_delay']//60}-{SAFE_ACTION_DELAYS['max_delay']//60} мин")
        
        return True
    
    async def load_account(self):
        """Загрузка данных аккаунта"""
        try:
            with open("accounts.csv", 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                accounts = list(reader)
            
            if not accounts:
                logger.error("Нет аккаунтов в файле!")
                return False
            
            account = accounts[0]
            self.phone = account['phone_number']
            
            logger.info(f"Загружен аккаунт: {self.phone}")
            return account
            
        except Exception as e:
            logger.error(f"Ошибка загрузки аккаунта: {e}")
            return False
    
    async def create_client(self, account):
        """Создание безопасного клиента"""
        try:
            api_id = int(account['api_id'])
            api_hash = account['api_hash']
            
            # Прокси (если есть)
            proxy = None
            if account.get('proxy_address'):
                proxy = {
                    'proxy_type': socks.SOCKS5,
                    'addr': account['proxy_address'],
                    'port': int(account['proxy_port']),
                    'username': account.get('proxy_user'),
                    'password': account.get('proxy_pass'),
                    'rdns': True
                }
                logger.info(f"Используется прокси: {account['proxy_address']}")
            else:
                logger.warning("⚠️  Прокси не настроен (для личного аккаунта рискованно)")
            
            # Создаем клиент с безопасными настройками
            session_path = Path("sessions") / f"{self.phone}.session"
            session_path.parent.mkdir(exist_ok=True)
            
            self.client = TelegramClient(
                str(session_path),
                api_id,
                api_hash,
                proxy=proxy,
                connection_retries=1,  # Минимум попыток
                retry_delay=60,        # Большая задержка при ошибках
                timeout=30,            # Короткий таймаут
                request_retries=1      # Минимум повторов запросов
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка создания клиента: {e}")
            return False
    
    async def safe_authorize(self):
        """Безопасная авторизация"""
        try:
            logger.info("🔗 Подключение к Telegram...")
            await self.client.connect()
            
            if not await self.client.is_user_authorized():
                logger.info("🔐 Требуется авторизация")
                
                # Отправляем код
                await self.client.send_code_request(self.phone)
                
                # Запрашиваем код у пользователя
                code = input(f"📱 Введите код для {self.phone}: ")
                
                try:
                    await self.client.sign_in(self.phone, code)
                except SessionPasswordNeededError:
                    password = input("🔒 Введите пароль 2FA: ")
                    await self.client.sign_in(password=password)
            
            # Получаем информацию о себе
            me = await self.client.get_me()
            logger.info(f"✅ Авторизован как: {me.first_name} (@{me.username or 'без username'})")
            
            return True
            
        except FloodWaitError as e:
            logger.error(f"❌ FloodWait: нужно подождать {e.seconds} секунд")
            return False
        except Exception as e:
            logger.error(f"❌ Ошибка авторизации: {e}")
            return False
    
    async def check_groups_access(self):
        """Проверка доступа к тестовым группам"""
        logger.info("🔍 Проверка доступа к тестовым группам...")
        
        accessible_groups = []
        
        for group in TEST_GROUPS:
            try:
                entity = await self.client.get_entity(group)
                permissions = await self.client.get_permissions(entity)
                
                logger.info(f"✅ Группа {group}: {entity.title}")
                logger.info(f"   Участников: {getattr(entity, 'participants_count', 'неизвестно')}")
                logger.info(f"   Права на сообщения: {'✅' if permissions.send_messages else '❌'}")
                
                if permissions.send_messages:
                    accessible_groups.append(group)
                else:
                    logger.warning(f"⚠️  Нет прав на отправку в {group}")
                
            except Exception as e:
                logger.error(f"❌ Группа {group}: {e}")
        
        return accessible_groups
    
    async def safe_test_action(self, action_type):
        """Безопасное выполнение тестового действия"""
        if self.actions_count >= FLOOD_PROTECTION["max_actions_per_day"]:
            logger.warning("⚠️  Достигнут дневной лимит действий")
            return False
        
        if self.errors_count >= SAFETY_MONITORING["max_errors_per_day"]:
            logger.warning("⚠️  Достигнут лимит ошибок за день")
            return False
        
        try:
            logger.info(f"🧪 Тестовое действие: {action_type}")
            
            if action_type == "comment":
                # Имитируем отправку комментария (БЕЗ реальной отправки)
                comment = SAFE_COMMENT_TEMPLATES[0]
                logger.info(f"   Комментарий: '{comment}'")
                logger.info("   ✅ (ИМИТАЦИЯ - реально не отправлено)")
            
            elif action_type == "reaction":
                # Имитируем реакцию (БЕЗ реальной постановки)
                reaction = SAFE_REACTIONS[0]
                logger.info(f"   Реакция: {reaction}")
                logger.info("   ✅ (ИМИТАЦИЯ - реально не поставлена)")
            
            self.actions_count += 1
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка в действии {action_type}: {e}")
            self.errors_count += 1
            return False
    
    async def run_safe_test(self):
        """Запуск безопасного теста"""
        logger.info("🛡️ НАЧАЛО БЕЗОПАСНОГО ТЕСТИРОВАНИЯ")
        logger.info("=" * 60)
        
        # Проверка безопасности
        if not await self.safety_check():
            return False
        
        # Загрузка аккаунта
        account = await self.load_account()
        if not account:
            return False
        
        # Создание клиента
        if not await self.create_client(account):
            return False
        
        # Авторизация
        if not await self.safe_authorize():
            await self.client.disconnect()
            return False
        
        # Проверка групп
        accessible_groups = await self.check_groups_access()
        
        # Интерактивное меню
        print("\n🎮 БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ")
        print("Выберите действие:")
        print("1. Только проверить подключение ✅")
        print("2. Имитировать комментарий (БЕЗ отправки)")
        print("3. Имитировать реакцию (БЕЗ постановки)")
        print("4. Выйти")
        
        choice = input("Ваш выбор (1-4): ")
        
        if choice == "1":
            logger.info("✅ Подключение работает! Тест завершен.")
        
        elif choice == "2":
            await self.safe_test_action("comment")
        
        elif choice == "3":
            await self.safe_test_action("reaction")
        
        elif choice == "4":
            logger.info("👋 Выход из тестирования")
        
        # Отключение
        await self.client.disconnect()
        logger.info("🛡️ БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО")
        
        return True


async def main():
    """Главная функция"""
    print("🛡️ БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ ЛИЧНОГО АККАУНТА")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Этот скрипт предназначен для ЛИЧНЫХ аккаунтов")
    print("🔒 Используются максимально безопасные настройки")
    print("📝 Перед началом убедитесь что:")
    print("   ✅ Настроен accounts.csv с вашими данными")
    print("   ✅ Указаны тестовые группы в safe_test_config.py")
    print("   ✅ У вас есть права в тестовых группах")
    print()
    
    confirm = input("Продолжить безопасное тестирование? (y/N): ")
    if confirm.lower() != 'y':
        print("👋 Тестирование отменено")
        return
    
    tester = SafePersonalAccountTester()
    
    try:
        await tester.run_safe_test()
    except KeyboardInterrupt:
        print("\n👋 Тестирование прервано пользователем")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
