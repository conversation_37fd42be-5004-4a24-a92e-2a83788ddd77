#!/usr/bin/env python3
"""
🔧 ИСПРАВЛЕНИЕ СЕССИИ
Пересоздание правильной сессии для аккаунта
"""

import asyncio
import logging
from pathlib import Path
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError
import socks

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - 🔧 FIX_SESSION - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def fix_session():
    """Исправляет сессию"""
    
    # Данные аккаунта
    phone = "+6283140647775"
    api_id = 24976030
    api_hash = "320690fc08a869f3e270f82a31954fa7"
    
    # Прокси SOCKS5
    proxy_config = {
        'proxy_type': socks.SOCKS5,
        'addr': '*************',
        'port': 64389,
        'username': 'Y1gC3qxS',
        'password': 'Wu4tmdT2',
        'rdns': True
    }
    
    print("🔧 ИСПРАВЛЕНИЕ СЕССИИ")
    print("=" * 40)
    print(f"📞 Номер: {phone}")
    print()
    
    # Удаляем старые сессии
    print("🗑️ Очистка старых сессий...")
    session_files = [
        f"sessions/{phone}.session",
        f"sessions/{phone}_proxy.session",
        f"sessions/{phone}_no_proxy.session",
        f"sessions/{phone}_with_proxy.session"
    ]
    
    for session_file in session_files:
        session_path = Path(session_file)
        if session_path.exists():
            session_path.unlink()
            print(f"   Удален: {session_file}")
    
    # Создаем новую сессию
    print("\n🔄 Создание новой сессии...")
    
    try:
        session_path = f"sessions/{phone}_fixed.session"
        client = TelegramClient(
            session_path,
            api_id,
            api_hash,
            proxy=proxy_config,
            device_model="Desktop",
            system_version="Windows 10",
            app_version="4.8.4",
            lang_code="en"
        )
        
        print("🔗 Подключение...")
        await client.connect()
        
        if not await client.is_user_authorized():
            print("🔐 Требуется повторная авторизация")
            print("📱 Отправляем новый код...")
            
            await client.send_code_request(phone)
            
            # Запрашиваем код
            code = input("📨 Введите новый код из Telegram: ").strip()
            
            try:
                await client.sign_in(phone, code)
            except SessionPasswordNeededError:
                password = input("🔒 Введите пароль 2FA: ").strip()
                await client.sign_in(password=password)
        
        # Проверяем авторизацию
        me = await client.get_me()
        
        if me:
            print(f"\n✅ СЕССИЯ ИСПРАВЛЕНА!")
            print(f"👤 Имя: {me.first_name}")
            if me.last_name:
                print(f"👤 Фамилия: {me.last_name}")
            print(f"📞 Телефон: {me.phone}")
            print(f"🆔 ID: {me.id}")
            
            # Тестируем базовые операции
            print(f"\n🧪 Тестирование операций...")
            
            # Получаем диалоги
            dialogs = await client.get_dialogs(limit=5)
            print(f"✅ Получено {len(dialogs)} диалогов")
            
            # Показываем первые диалоги
            print(f"\n💬 Ваши диалоги:")
            for i, dialog in enumerate(dialogs[:5], 1):
                if dialog.is_group:
                    type_icon = "👥"
                elif dialog.is_channel:
                    type_icon = "📢"
                else:
                    type_icon = "👤"
                
                print(f"   {i}. {type_icon} {dialog.name}")
                if hasattr(dialog.entity, 'username') and dialog.entity.username:
                    print(f"      @{dialog.entity.username}")
            
            await client.disconnect()
            
            print(f"\n🎉 СЕССИЯ ГОТОВА К ИСПОЛЬЗОВАНИЮ!")
            print(f"📁 Файл сессии: {session_path}")
            
            # Обновляем accounts.csv
            print(f"\n📝 Обновление accounts.csv...")
            
            import csv
            data = [
                ["phone_number", "api_id", "api_hash", "proxy_address", "proxy_port", "proxy_user", "proxy_pass"],
                [phone, api_id, api_hash, "*************", "64389", "Y1gC3qxS", "Wu4tmdT2"]
            ]
            
            with open("accounts.csv", "w", newline="", encoding="utf-8") as file:
                writer = csv.writer(file)
                writer.writerows(data)
            
            print("✅ accounts.csv обновлен")
            
            return True
        else:
            print("❌ Не удалось получить информацию о пользователе")
            await client.disconnect()
            return False
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        if 'client' in locals():
            await client.disconnect()
        return False

async def main():
    """Главная функция"""
    success = await fix_session()
    
    if success:
        print("\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО!")
        print("=" * 40)
        print("✅ Сессия создана заново")
        print("✅ Авторизация проверена")
        print("✅ Базовые операции работают")
        print()
        print("📋 ТЕПЕРЬ МОЖНО:")
        print("1. Запустить безопасное тестирование:")
        print("   python safe_personal_test.py")
        print()
        print("2. Или запустить оптимизированную ферму:")
        print("   python main_optimized.py")
    else:
        print("\n❌ Исправление не завершено")

if __name__ == "__main__":
    asyncio.run(main())
