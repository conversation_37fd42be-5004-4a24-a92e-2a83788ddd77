"""
REST API сервер для управления Telegram фермой
Предоставляет веб-интерфейс для мониторинга и управления аккаунтами
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from main import FarmOrchestrator, AccountManager
from modules.ai_generator import ai_generator
from modules.commenter import get_comment_stats
from modules.dialogue import get_dialogue_stats
from modules.reactor import get_reaction_stats

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Создаем FastAPI приложение
app = FastAPI(
    title="Telegram Farm API",
    description="API для управления фермой Telegram аккаунтов",
    version="2.0.0"
)

# Настройка CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Глобальные переменные
farm_orchestrator: Optional[FarmOrchestrator] = None
websocket_connections: List[WebSocket] = []


# Модели данных
class AccountStatus(BaseModel):
    phone: str
    status: str  # active, inactive, error
    last_action: Optional[datetime]
    stats: Dict[str, int]


class FarmStats(BaseModel):
    total_accounts: int
    active_accounts: int
    total_comments: int
    total_reactions: int
    total_dialogues: int
    total_errors: int
    ai_stats: Dict[str, Any]


class AccountConfig(BaseModel):
    phone: str
    enabled: bool
    limits: Dict[str, int]
    groups: List[str]


class AIConfig(BaseModel):
    provider: str
    enabled: bool
    fallback_enabled: bool


# WebSocket менеджер
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except:
                self.disconnect(connection)


manager = ConnectionManager()


# API эндпоинты
@app.get("/")
async def root():
    """Главная страница"""
    return {"message": "Telegram Farm API v2.0", "status": "running"}


@app.get("/api/status")
async def get_farm_status():
    """Получить общий статус фермы"""
    global farm_orchestrator
    
    if not farm_orchestrator:
        return {"status": "stopped", "message": "Ферма не запущена"}
    
    return {
        "status": "running" if farm_orchestrator.running else "stopped",
        "accounts_count": len(farm_orchestrator.account_manager.active_accounts),
        "uptime": datetime.now().isoformat()
    }


@app.get("/api/stats", response_model=FarmStats)
async def get_farm_stats():
    """Получить статистику фермы"""
    global farm_orchestrator
    
    if not farm_orchestrator:
        raise HTTPException(status_code=404, detail="Ферма не запущена")
    
    account_manager = farm_orchestrator.account_manager
    
    return FarmStats(
        total_accounts=len(account_manager.accounts),
        active_accounts=len(account_manager.active_accounts),
        total_comments=farm_orchestrator.stats.get("comments", 0),
        total_reactions=farm_orchestrator.stats.get("reactions", 0),
        total_dialogues=farm_orchestrator.stats.get("dialogues", 0),
        total_errors=farm_orchestrator.stats.get("errors", 0),
        ai_stats=ai_generator.get_stats()
    )


@app.get("/api/accounts", response_model=List[AccountStatus])
async def get_accounts():
    """Получить список всех аккаунтов"""
    global farm_orchestrator
    
    if not farm_orchestrator:
        raise HTTPException(status_code=404, detail="Ферма не запущена")
    
    accounts = []
    account_manager = farm_orchestrator.account_manager
    
    for account in account_manager.accounts:
        phone = account['phone_number']
        
        # Определяем статус
        if phone in account_manager.active_accounts:
            status = "active"
        else:
            status = "inactive"
        
        # Получаем статистику
        comment_stats = get_comment_stats(phone)
        reaction_stats = get_reaction_stats(phone)
        dialogue_stats = get_dialogue_stats(phone)
        
        combined_stats = {
            **comment_stats,
            **reaction_stats,
            **dialogue_stats
        }
        
        accounts.append(AccountStatus(
            phone=phone,
            status=status,
            last_action=account_manager.account_last_action.get(phone),
            stats=combined_stats
        ))
    
    return accounts


@app.post("/api/farm/start")
async def start_farm():
    """Запустить ферму"""
    global farm_orchestrator
    
    if farm_orchestrator and farm_orchestrator.running:
        raise HTTPException(status_code=400, detail="Ферма уже запущена")
    
    try:
        farm_orchestrator = FarmOrchestrator()
        
        # Запускаем в фоновом режиме
        asyncio.create_task(farm_orchestrator.start())
        
        await manager.broadcast({
            "type": "farm_status",
            "status": "starting",
            "timestamp": datetime.now().isoformat()
        })
        
        return {"message": "Ферма запускается"}
    
    except Exception as e:
        logger.error(f"Ошибка запуска фермы: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/farm/stop")
async def stop_farm():
    """Остановить ферму"""
    global farm_orchestrator
    
    if not farm_orchestrator or not farm_orchestrator.running:
        raise HTTPException(status_code=400, detail="Ферма не запущена")
    
    try:
        await farm_orchestrator.stop()
        
        await manager.broadcast({
            "type": "farm_status",
            "status": "stopped",
            "timestamp": datetime.now().isoformat()
        })
        
        return {"message": "Ферма остановлена"}
    
    except Exception as e:
        logger.error(f"Ошибка остановки фермы: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/ai/stats")
async def get_ai_stats():
    """Получить статистику AI"""
    return ai_generator.get_stats()


@app.post("/api/ai/config")
async def update_ai_config(config: AIConfig):
    """Обновить конфигурацию AI"""
    try:
        ai_generator.set_fallback_enabled(config.fallback_enabled)
        
        await manager.broadcast({
            "type": "ai_config_updated",
            "config": config.dict(),
            "timestamp": datetime.now().isoformat()
        })
        
        return {"message": "Конфигурация AI обновлена"}
    
    except Exception as e:
        logger.error(f"Ошибка обновления AI конфигурации: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket для real-time обновлений"""
    await manager.connect(websocket)
    
    try:
        while True:
            # Отправляем периодические обновления
            if farm_orchestrator:
                stats = {
                    "type": "stats_update",
                    "data": {
                        "active_accounts": len(farm_orchestrator.account_manager.active_accounts),
                        "total_comments": farm_orchestrator.stats.get("comments", 0),
                        "total_reactions": farm_orchestrator.stats.get("reactions", 0),
                        "total_dialogues": farm_orchestrator.stats.get("dialogues", 0),
                        "total_errors": farm_orchestrator.stats.get("errors", 0),
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
                await websocket.send_text(json.dumps(stats))
            
            await asyncio.sleep(5)  # Обновления каждые 5 секунд
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)


# Статические файлы для веб-интерфейса
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """Веб-интерфейс управления"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Telegram Farm Dashboard</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .container { max-width: 1200px; margin: 0 auto; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: #f5f5f5; padding: 20px; border-radius: 8px; text-align: center; }
            .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
            .accounts-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            .accounts-table th, .accounts-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            .accounts-table th { background-color: #f2f2f2; }
            .status-active { color: green; font-weight: bold; }
            .status-inactive { color: red; }
            .controls { margin-bottom: 20px; }
            .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
            .btn-primary { background-color: #007bff; color: white; }
            .btn-danger { background-color: #dc3545; color: white; }
            .btn:hover { opacity: 0.8; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Telegram Farm Dashboard</h1>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startFarm()">Запустить ферму</button>
                <button class="btn btn-danger" onclick="stopFarm()">Остановить ферму</button>
                <span id="status">Загрузка...</span>
            </div>
            
            <div class="stats" id="stats">
                <!-- Статистика будет загружена через JavaScript -->
            </div>
            
            <h2>Аккаунты</h2>
            <table class="accounts-table" id="accounts-table">
                <thead>
                    <tr>
                        <th>Телефон</th>
                        <th>Статус</th>
                        <th>Комментарии</th>
                        <th>Реакции</th>
                        <th>Диалоги</th>
                        <th>Последнее действие</th>
                    </tr>
                </thead>
                <tbody id="accounts-body">
                    <!-- Аккаунты будут загружены через JavaScript -->
                </tbody>
            </table>
        </div>
        
        <script>
            // WebSocket подключение для real-time обновлений
            const ws = new WebSocket('ws://localhost:8000/ws');
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'stats_update') {
                    updateStats(data.data);
                }
            };
            
            // Функции управления
            async function startFarm() {
                try {
                    const response = await fetch('/api/farm/start', { method: 'POST' });
                    const result = await response.json();
                    alert(result.message);
                    loadData();
                } catch (error) {
                    alert('Ошибка: ' + error.message);
                }
            }
            
            async function stopFarm() {
                try {
                    const response = await fetch('/api/farm/stop', { method: 'POST' });
                    const result = await response.json();
                    alert(result.message);
                    loadData();
                } catch (error) {
                    alert('Ошибка: ' + error.message);
                }
            }
            
            // Загрузка данных
            async function loadData() {
                try {
                    // Загружаем статус
                    const statusResponse = await fetch('/api/status');
                    const status = await statusResponse.json();
                    document.getElementById('status').textContent = `Статус: ${status.status}`;
                    
                    // Загружаем статистику
                    const statsResponse = await fetch('/api/stats');
                    const stats = await statsResponse.json();
                    updateStats(stats);
                    
                    // Загружаем аккаунты
                    const accountsResponse = await fetch('/api/accounts');
                    const accounts = await accountsResponse.json();
                    updateAccounts(accounts);
                    
                } catch (error) {
                    console.error('Ошибка загрузки данных:', error);
                }
            }
            
            function updateStats(stats) {
                const statsContainer = document.getElementById('stats');
                statsContainer.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-value">${stats.active_accounts || 0}</div>
                        <div>Активных аккаунтов</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_comments || 0}</div>
                        <div>Комментариев</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_reactions || 0}</div>
                        <div>Реакций</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_dialogues || 0}</div>
                        <div>Диалогов</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_errors || 0}</div>
                        <div>Ошибок</div>
                    </div>
                `;
            }
            
            function updateAccounts(accounts) {
                const tbody = document.getElementById('accounts-body');
                tbody.innerHTML = accounts.map(account => `
                    <tr>
                        <td>${account.phone}</td>
                        <td class="status-${account.status}">${account.status}</td>
                        <td>${account.stats.comments || 0}</td>
                        <td>${account.stats.reactions || 0}</td>
                        <td>${account.stats.dialogues_today || 0}</td>
                        <td>${account.last_action ? new Date(account.last_action).toLocaleString() : 'Никогда'}</td>
                    </tr>
                `).join('');
            }
            
            // Загружаем данные при загрузке страницы
            loadData();
            
            // Обновляем данные каждые 10 секунд
            setInterval(loadData, 10000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
