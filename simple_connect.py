#!/usr/bin/env python3
"""
🛡️ ПРОСТОЕ ПОДКЛЮЧЕНИЕ К TELEGRAM
Авторизация аккаунта +6283140647775
"""

import asyncio
import logging
from pathlib import Path
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError
import socks

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def connect_and_authorize():
    """Подключается и авторизует аккаунт"""
    
    # Данные аккаунта
    phone = "+6283140647775"
    api_id = 24976030
    api_hash = "320690fc08a869f3e270f82a31954fa7"
    
    print("🚀 ПОДКЛЮЧЕНИЕ К TELEGRAM")
    print("=" * 40)
    print(f"📞 Номер: {phone}")
    print(f"🔑 API ID: {api_id}")
    print()
    
    # Создаем папку для сессий
    Path("sessions").mkdir(exist_ok=True)
    
    # Сначала пробуем без прокси
    print("🔍 Попытка 1: Подключение БЕЗ прокси...")
    
    try:
        session_path = f"sessions/{phone}.session"
        client = TelegramClient(
            session_path,
            api_id,
            api_hash,
            device_model="Desktop",
            system_version="Windows 10",
            app_version="4.8.4",
            lang_code="en"
        )
        
        print("🔗 Подключение...")
        await client.connect()
        
        if client.is_connected():
            print("✅ Подключение успешно!")
            
            if not await client.is_user_authorized():
                print("🔐 Требуется авторизация")
                print("📱 Отправляем код...")
                
                await client.send_code_request(phone)
                
                # Запрашиваем код
                code = input("📨 Введите код из Telegram: ").strip()
                
                try:
                    await client.sign_in(phone, code)
                except SessionPasswordNeededError:
                    password = input("🔒 Введите пароль 2FA: ").strip()
                    await client.sign_in(password=password)
            
            # Получаем информацию о пользователе
            me = await client.get_me()
            
            print(f"\n🎉 АВТОРИЗАЦИЯ УСПЕШНА!")
            print(f"👤 Имя: {me.first_name}")
            if me.last_name:
                print(f"👤 Фамилия: {me.last_name}")
            print(f"📞 Телефон: {me.phone}")
            print(f"🆔 ID: {me.id}")
            if me.username:
                print(f"👤 Username: @{me.username}")
            
            print(f"\n✅ Сессия сохранена в: {session_path}")
            
            await client.disconnect()
            return True
            
        else:
            print("❌ Не удалось подключиться")
            await client.disconnect()
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        if 'client' in locals():
            await client.disconnect()
    
    # Если без прокси не получилось, пробуем с прокси
    print("\n🔍 Попытка 2: Подключение С прокси SOCKS5...")
    
    try:
        proxy_config = {
            'proxy_type': socks.SOCKS5,
            'addr': '176.103.95.80',
            'port': 64389,
            'username': 'Y1gC3qxS',
            'password': 'Wu4tmdT2',
            'rdns': True
        }
        
        session_path = f"sessions/{phone}_proxy.session"
        client = TelegramClient(
            session_path,
            api_id,
            api_hash,
            proxy=proxy_config,
            device_model="Desktop",
            system_version="Windows 10",
            app_version="4.8.4",
            lang_code="en"
        )
        
        print(f"🌐 Подключение через прокси {proxy_config['addr']}:{proxy_config['port']}...")
        await client.connect()
        
        if client.is_connected():
            print("✅ Подключение через прокси успешно!")
            
            if not await client.is_user_authorized():
                print("🔐 Требуется авторизация")
                print("📱 Отправляем код...")
                
                await client.send_code_request(phone)
                
                # Запрашиваем код
                code = input("📨 Введите код из Telegram: ").strip()
                
                try:
                    await client.sign_in(phone, code)
                except SessionPasswordNeededError:
                    password = input("🔒 Введите пароль 2FA: ").strip()
                    await client.sign_in(password=password)
            
            # Получаем информацию о пользователе
            me = await client.get_me()
            
            print(f"\n🎉 АВТОРИЗАЦИЯ ЧЕРЕЗ ПРОКСИ УСПЕШНА!")
            print(f"👤 Имя: {me.first_name}")
            if me.last_name:
                print(f"👤 Фамилия: {me.last_name}")
            print(f"📞 Телефон: {me.phone}")
            print(f"🆔 ID: {me.id}")
            if me.username:
                print(f"👤 Username: @{me.username}")
            
            print(f"\n✅ Сессия сохранена в: {session_path}")
            
            await client.disconnect()
            return True
            
        else:
            print("❌ Не удалось подключиться через прокси")
            await client.disconnect()
            
    except Exception as e:
        print(f"❌ Ошибка с прокси: {e}")
        if 'client' in locals():
            await client.disconnect()
    
    print("\n❌ Все попытки подключения неудачны")
    return False

async def main():
    """Главная функция"""
    success = await connect_and_authorize()
    
    if success:
        print("\n🎉 НАСТРОЙКА ЗАВЕРШЕНА!")
        print("=" * 40)
        print("✅ Аккаунт авторизован")
        print("✅ Сессия сохранена")
        print("✅ Готов к использованию")
        print()
        print("📋 СЛЕДУЮЩИЕ ШАГИ:")
        print("1. Запустите безопасное тестирование:")
        print("   python safe_personal_test.py")
        print()
        print("2. Или запустите оптимизированную ферму:")
        print("   python main_optimized.py")
        print()
        print("⚠️ ВАЖНО: Используйте только безопасные лимиты!")
    else:
        print("\n❌ Настройка не завершена")
        print("🔧 Проверьте интернет и API ключи")

if __name__ == "__main__":
    asyncio.run(main())
