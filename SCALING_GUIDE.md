# 📈 Руководство по масштабированию Telegram-фермы

## 🎯 Стратегия масштабирования

### Этапы роста:
1. **Тестовый этап**: 2-3 аккаунта
2. **Малый масштаб**: 5-10 аккаунтов
3. **Средний масштаб**: 20-50 аккаунтов
4. **Большой масштаб**: 100+ аккаунтов

### Принципы безопасного масштабирования:
- **Постепенное увеличение** количества аккаунтов
- **Мониторинг производительности** на каждом этапе
- **Тестирование стабильности** перед добавлением новых аккаунтов
- **Резервное копирование** конфигураций

## 🔧 Добавление новых аккаунтов

### 1. Подготовка аккаунтов

#### 1.1 Требования к новым аккаунтам:
- **Возраст аккаунта**: минимум 1 месяц
- **Активность**: должны иметь историю использования
- **Верификация**: желательно привязка к номеру телефона
- **Чистая репутация**: без предыдущих блокировок

#### 1.2 Получение API ключей:
```bash
# Для каждого нового аккаунта:
# 1. Перейдите на https://my.telegram.org
# 2. Войдите с номером телефона
# 3. Создайте новое приложение
# 4. Сохраните api_id и api_hash
```

### 2. Настройка прокси

#### 2.1 Требования к прокси:
- **Уникальный прокси** для каждого аккаунта
- **Высокая скорость** (ping < 100ms)
- **Стабильность** (uptime > 99%)
- **Географическое разнообразие**

#### 2.2 Рекомендуемое распределение:
```
Аккаунты 1-10:   Прокси из России
Аккаунты 11-20:  Прокси из Украины
Аккаунты 21-30:  Прокси из Казахстана
Аккаунты 31+:    Прокси из других стран СНГ
```

#### 2.3 Проверка качества прокси:
```bash
# Скрипт для проверки всех прокси
python -c "
import csv
import requests
import time

with open('accounts.csv', 'r') as f:
    reader = csv.DictReader(f)
    for row in reader:
        if row['proxy_address']:
            proxy = {
                'http': f'socks5://{row[\"proxy_user\"]}:{row[\"proxy_pass\"]}@{row[\"proxy_address\"]}:{row[\"proxy_port\"]}',
                'https': f'socks5://{row[\"proxy_user\"]}:{row[\"proxy_pass\"]}@{row[\"proxy_address\"]}:{row[\"proxy_port\"]}'
            }
            try:
                start = time.time()
                r = requests.get('https://httpbin.org/ip', proxies=proxy, timeout=10)
                ping = (time.time() - start) * 1000
                print(f'{row[\"phone_number\"]}: OK (ping: {ping:.0f}ms)')
            except Exception as e:
                print(f'{row[\"phone_number\"]}: FAILED - {e}')
"
```

### 3. Обновление конфигурации

#### 3.1 Добавление в accounts.csv:
```csv
# Добавляйте по одному аккаунту за раз
+***********,********,new_api_hash,proxy4.com,1080,user4,pass4
```

#### 3.2 Постепенное увеличение лимитов:
```python
# config.py - для 5-10 аккаунтов
DAILY_LIMITS = {
    "comments": 3,
    "reactions": 8,
    "dialogues": 1,
}

# config.py - для 20-50 аккаунтов
DAILY_LIMITS = {
    "comments": 5,
    "reactions": 15,
    "dialogues": 3,
}

# config.py - для 100+ аккаунтов
DAILY_LIMITS = {
    "comments": 8,
    "reactions": 25,
    "dialogues": 5,
}
```

## ⚡ Оптимизация производительности

### 1. Системные требования по масштабу

#### Малый масштаб (5-10 аккаунтов):
- **RAM**: 4-8 GB
- **CPU**: 2-4 ядра
- **Диск**: 10 GB SSD
- **Сеть**: 10 Mbps

#### Средний масштаб (20-50 аккаунтов):
- **RAM**: 16-32 GB
- **CPU**: 4-8 ядер
- **Диск**: 50 GB SSD
- **Сеть**: 50 Mbps

#### Большой масштаб (100+ аккаунтов):
- **RAM**: 64+ GB
- **CPU**: 8+ ядер
- **Диск**: 100+ GB NVMe SSD
- **Сеть**: 100+ Mbps

### 2. Оптимизация задержек

#### 2.1 Адаптивные задержки по количеству аккаунтов:
```python
# Функция для расчета оптимальных задержек
def calculate_delays(account_count):
    if account_count <= 10:
        return {"min_delay": 15 * 60, "max_delay": 30 * 60}
    elif account_count <= 50:
        return {"min_delay": 10 * 60, "max_delay": 20 * 60}
    else:
        return {"min_delay": 5 * 60, "max_delay": 15 * 60}
```

#### 2.2 Балансировка нагрузки:
```python
# Распределение аккаунтов по временным слотам
def distribute_accounts_by_time(accounts, time_slots=24):
    slot_size = len(accounts) // time_slots
    return [accounts[i:i+slot_size] for i in range(0, len(accounts), slot_size)]
```

### 3. Использование расширенной версии

#### 3.1 Переход на main_v2.py:
```bash
# Для масштабирования используйте расширенную версию
python main_v2.py
```

#### 3.2 Настройка пулов подключений:
```python
# В modules/scalable_farm.py
POOL_CONFIG = {
    "max_connections_per_pool": 10,
    "pools_count": account_count // 10,
    "connection_timeout": 30,
    "read_timeout": 60,
}
```

## 📊 Мониторинг и аналитика

### 1. Веб-интерфейс

#### 1.1 Запуск API сервера:
```bash
# Терминал 1: API сервер
python api_server.py

# Терминал 2: Ферма
python main_v2.py

# Браузер: http://localhost:8000/dashboard
```

#### 1.2 Ключевые метрики:
- **Активные аккаунты**: количество работающих аккаунтов
- **Успешность действий**: процент успешных операций
- **Средняя задержка**: время между действиями
- **Ошибки**: количество и типы ошибок

### 2. Система алертов

#### 2.1 Настройка уведомлений:
```python
# В config.py добавьте
MONITORING_CONFIG = {
    "alert_on_errors": True,
    "max_error_rate": 0.1,  # 10% ошибок
    "alert_email": "<EMAIL>",
    "telegram_bot_token": "your_bot_token",
    "telegram_chat_id": "your_chat_id",
}
```

#### 2.2 Автоматические действия:
- **Отключение проблемных аккаунтов**
- **Увеличение задержек при ошибках**
- **Переключение на резервные прокси**

### 3. Логирование

#### 3.1 Структурированные логи:
```python
# Настройка детального логирования
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": {
        "file": {
            "filename": "farm.log",
            "max_bytes": ********,  # 10MB
            "backup_count": 5
        },
        "console": {
            "level": "INFO"
        }
    }
}
```

## 🔄 Распределение нагрузки

### 1. Временное распределение

#### 1.1 Создание расписания:
```python
# Распределение активности по часам
ACTIVITY_SCHEDULE = {
    "morning": {"start": 8, "end": 12, "accounts_percent": 0.3},
    "afternoon": {"start": 12, "end": 18, "accounts_percent": 0.4},
    "evening": {"start": 18, "end": 23, "accounts_percent": 0.3},
}
```

#### 1.2 Ротация аккаунтов:
```python
def rotate_active_accounts(all_accounts, rotation_hours=6):
    """Ротация активных аккаунтов каждые N часов"""
    current_hour = datetime.now().hour
    rotation_index = (current_hour // rotation_hours) % len(all_accounts)
    accounts_per_rotation = len(all_accounts) // 4
    
    start_idx = rotation_index * accounts_per_rotation
    end_idx = start_idx + accounts_per_rotation
    
    return all_accounts[start_idx:end_idx]
```

### 2. Географическое распределение

#### 2.1 Группировка по регионам:
```python
PROXY_REGIONS = {
    "russia": {"accounts": [], "timezone": "Europe/Moscow"},
    "ukraine": {"accounts": [], "timezone": "Europe/Kiev"},
    "kazakhstan": {"accounts": [], "timezone": "Asia/Almaty"},
}
```

#### 2.2 Учет часовых поясов:
```python
def get_local_time(region):
    """Получение локального времени для региона"""
    import pytz
    tz = pytz.timezone(PROXY_REGIONS[region]["timezone"])
    return datetime.now(tz)
```

## 🛡️ Безопасность при масштабировании

### 1. Принципы безопасности

#### 1.1 Изоляция аккаунтов:
- **Уникальные прокси** для каждого аккаунта
- **Разные API ключи** (если возможно)
- **Изолированные сессии** в отдельных папках

#### 1.2 Мониторинг рисков:
```python
RISK_MONITORING = {
    "max_actions_per_hour": 100,
    "max_errors_per_account": 5,
    "cooldown_on_flood_wait": 3600,  # 1 час
    "auto_disable_on_ban": True,
}
```

### 2. Стратегии снижения рисков

#### 2.1 Постепенное увеличение активности:
```python
def calculate_safe_limits(account_age_days, current_limits):
    """Расчет безопасных лимитов на основе возраста аккаунта"""
    if account_age_days < 30:
        return {k: max(1, v // 3) for k, v in current_limits.items()}
    elif account_age_days < 90:
        return {k: max(1, v // 2) for k, v in current_limits.items()}
    else:
        return current_limits
```

#### 2.2 Система резервных копий:
```bash
# Автоматическое резервное копирование
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p backups/$DATE
cp accounts.csv backups/$DATE/
cp config.py backups/$DATE/
cp -r sessions/ backups/$DATE/
echo "Backup created: backups/$DATE"
```

## 📈 Метрики производительности

### 1. KPI для мониторинга:
- **Успешность действий**: > 95%
- **Время отклика**: < 30 секунд
- **Использование CPU**: < 80%
- **Использование RAM**: < 90%
- **Ошибки сети**: < 5%

### 2. Оптимизация по метрикам:
```python
def optimize_based_on_metrics(metrics):
    """Автоматическая оптимизация на основе метрик"""
    if metrics["success_rate"] < 0.95:
        increase_delays()
    if metrics["cpu_usage"] > 0.8:
        reduce_concurrent_operations()
    if metrics["memory_usage"] > 0.9:
        cleanup_old_sessions()
```

## ✅ Чек-лист масштабирования

Перед добавлением новых аккаунтов:
- [ ] Текущие аккаунты работают стабильно
- [ ] Нет критических ошибок в логах
- [ ] Прокси для новых аккаунтов протестированы
- [ ] API ключи получены и проверены
- [ ] Системные ресурсы позволяют увеличение
- [ ] Мониторинг настроен и работает
- [ ] Резервные копии созданы
- [ ] Лимиты адаптированы под новый масштаб

## 🚀 Готово к масштабированию!

Следуйте этому руководству для безопасного и эффективного масштабирования вашей Telegram-фермы.
