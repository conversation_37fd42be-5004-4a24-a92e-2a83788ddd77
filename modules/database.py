"""
Асинхронная система базы данных для замены файлового хранения
Оптимизирует производительность и обеспечивает надежность данных
"""

import asyncio
import aiosqlite
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import sqlite3

logger = logging.getLogger(__name__)


@dataclass
class AccountRecord:
    """Запись аккаунта"""
    phone_number: str
    api_id: int
    api_hash: str
    proxy_address: Optional[str] = None
    proxy_port: Optional[int] = None
    proxy_user: Optional[str] = None
    proxy_pass: Optional[str] = None
    is_active: bool = True
    created_at: datetime = None
    last_action: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class ActionRecord:
    """Запись о действии"""
    id: Optional[int] = None
    account_phone: str = ""
    action_type: str = ""  # comment, reaction, dialogue
    target_group: str = ""
    target_message_id: Optional[int] = None
    content: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    execution_time: float = 0.0
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class StatsRecord:
    """Запись статистики"""
    account_phone: str
    date: str  # YYYY-MM-DD
    comments: int = 0
    reactions: int = 0
    dialogues: int = 0
    errors: int = 0
    total_execution_time: float = 0.0
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.updated_at is None:
            self.updated_at = datetime.now()


class AsyncDatabase:
    """Асинхронная база данных для фермы"""
    
    def __init__(self, db_path: str = "farm_data.db"):
        self.db_path = db_path
        self.connection: Optional[aiosqlite.Connection] = None
        self._lock = asyncio.Lock()
        
        # Пул соединений для оптимизации
        self.connection_pool: List[aiosqlite.Connection] = []
        self.max_connections = 5
        self.pool_semaphore = asyncio.Semaphore(self.max_connections)
    
    async def initialize(self):
        """Инициализирует базу данных"""
        logger.info(f"Инициализация базы данных: {self.db_path}")
        
        # Создаем основное соединение
        self.connection = await aiosqlite.connect(self.db_path)
        self.connection.row_factory = aiosqlite.Row
        
        # Создаем таблицы
        await self._create_tables()
        
        # Инициализируем пул соединений
        await self._init_connection_pool()
        
        logger.info("База данных инициализирована")
    
    async def _create_tables(self):
        """Создает таблицы базы данных"""
        async with self.connection.execute("PRAGMA foreign_keys = ON"):
            pass
        
        # Таблица аккаунтов
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                phone_number TEXT PRIMARY KEY,
                api_id INTEGER NOT NULL,
                api_hash TEXT NOT NULL,
                proxy_address TEXT,
                proxy_port INTEGER,
                proxy_user TEXT,
                proxy_pass TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_action TIMESTAMP
            )
        """)
        
        # Таблица действий
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_phone TEXT NOT NULL,
                action_type TEXT NOT NULL,
                target_group TEXT NOT NULL,
                target_message_id INTEGER,
                content TEXT,
                success BOOLEAN DEFAULT TRUE,
                error_message TEXT,
                execution_time REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_phone) REFERENCES accounts (phone_number)
            )
        """)
        
        # Таблица статистики
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS stats (
                account_phone TEXT NOT NULL,
                date TEXT NOT NULL,
                comments INTEGER DEFAULT 0,
                reactions INTEGER DEFAULT 0,
                dialogues INTEGER DEFAULT 0,
                errors INTEGER DEFAULT 0,
                total_execution_time REAL DEFAULT 0.0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (account_phone, date),
                FOREIGN KEY (account_phone) REFERENCES accounts (phone_number)
            )
        """)
        
        # Индексы для оптимизации
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_actions_account_date 
            ON actions (account_phone, created_at)
        """)
        
        await self.connection.execute("""
            CREATE INDEX IF NOT EXISTS idx_actions_type_date 
            ON actions (action_type, created_at)
        """)
        
        await self.connection.commit()
    
    async def _init_connection_pool(self):
        """Инициализирует пул соединений"""
        for _ in range(self.max_connections):
            conn = await aiosqlite.connect(self.db_path)
            conn.row_factory = aiosqlite.Row
            self.connection_pool.append(conn)
    
    async def get_connection(self) -> aiosqlite.Connection:
        """Получает соединение из пула"""
        async with self.pool_semaphore:
            if self.connection_pool:
                return self.connection_pool.pop()
            else:
                # Создаем новое соединение если пул пуст
                conn = await aiosqlite.connect(self.db_path)
                conn.row_factory = aiosqlite.Row
                return conn
    
    async def return_connection(self, conn: aiosqlite.Connection):
        """Возвращает соединение в пул"""
        if len(self.connection_pool) < self.max_connections:
            self.connection_pool.append(conn)
        else:
            await conn.close()
    
    # Методы для работы с аккаунтами
    
    async def save_account(self, account: AccountRecord) -> bool:
        """Сохраняет аккаунт в базу данных"""
        try:
            conn = await self.get_connection()
            try:
                await conn.execute("""
                    INSERT OR REPLACE INTO accounts 
                    (phone_number, api_id, api_hash, proxy_address, proxy_port, 
                     proxy_user, proxy_pass, is_active, created_at, last_action)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    account.phone_number, account.api_id, account.api_hash,
                    account.proxy_address, account.proxy_port,
                    account.proxy_user, account.proxy_pass,
                    account.is_active, account.created_at, account.last_action
                ))
                await conn.commit()
                return True
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка сохранения аккаунта {account.phone_number}: {e}")
            return False
    
    async def get_account(self, phone_number: str) -> Optional[AccountRecord]:
        """Получает аккаунт по номеру телефона"""
        try:
            conn = await self.get_connection()
            try:
                async with conn.execute(
                    "SELECT * FROM accounts WHERE phone_number = ?", 
                    (phone_number,)
                ) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        return AccountRecord(
                            phone_number=row['phone_number'],
                            api_id=row['api_id'],
                            api_hash=row['api_hash'],
                            proxy_address=row['proxy_address'],
                            proxy_port=row['proxy_port'],
                            proxy_user=row['proxy_user'],
                            proxy_pass=row['proxy_pass'],
                            is_active=bool(row['is_active']),
                            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                            last_action=datetime.fromisoformat(row['last_action']) if row['last_action'] else None
                        )
                    return None
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка получения аккаунта {phone_number}: {e}")
            return None
    
    async def get_all_accounts(self, active_only: bool = True) -> List[AccountRecord]:
        """Получает все аккаунты"""
        try:
            conn = await self.get_connection()
            try:
                query = "SELECT * FROM accounts"
                params = ()
                
                if active_only:
                    query += " WHERE is_active = ?"
                    params = (True,)
                
                accounts = []
                async with conn.execute(query, params) as cursor:
                    async for row in cursor:
                        account = AccountRecord(
                            phone_number=row['phone_number'],
                            api_id=row['api_id'],
                            api_hash=row['api_hash'],
                            proxy_address=row['proxy_address'],
                            proxy_port=row['proxy_port'],
                            proxy_user=row['proxy_user'],
                            proxy_pass=row['proxy_pass'],
                            is_active=bool(row['is_active']),
                            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                            last_action=datetime.fromisoformat(row['last_action']) if row['last_action'] else None
                        )
                        accounts.append(account)
                
                return accounts
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка получения аккаунтов: {e}")
            return []
    
    async def update_account_last_action(self, phone_number: str, action_time: datetime = None):
        """Обновляет время последнего действия аккаунта"""
        if action_time is None:
            action_time = datetime.now()
        
        try:
            conn = await self.get_connection()
            try:
                await conn.execute(
                    "UPDATE accounts SET last_action = ? WHERE phone_number = ?",
                    (action_time, phone_number)
                )
                await conn.commit()
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка обновления времени действия для {phone_number}: {e}")
    
    # Методы для работы с действиями
    
    async def save_action(self, action: ActionRecord) -> Optional[int]:
        """Сохраняет действие в базу данных"""
        try:
            conn = await self.get_connection()
            try:
                cursor = await conn.execute("""
                    INSERT INTO actions 
                    (account_phone, action_type, target_group, target_message_id, 
                     content, success, error_message, execution_time, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    action.account_phone, action.action_type, action.target_group,
                    action.target_message_id, action.content, action.success,
                    action.error_message, action.execution_time, action.created_at
                ))
                await conn.commit()
                return cursor.lastrowid
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка сохранения действия: {e}")
            return None
    
    async def get_actions(
        self, 
        account_phone: str = None, 
        action_type: str = None,
        since: datetime = None,
        limit: int = 100
    ) -> List[ActionRecord]:
        """Получает действия с фильтрацией"""
        try:
            conn = await self.get_connection()
            try:
                query = "SELECT * FROM actions WHERE 1=1"
                params = []
                
                if account_phone:
                    query += " AND account_phone = ?"
                    params.append(account_phone)
                
                if action_type:
                    query += " AND action_type = ?"
                    params.append(action_type)
                
                if since:
                    query += " AND created_at >= ?"
                    params.append(since)
                
                query += " ORDER BY created_at DESC LIMIT ?"
                params.append(limit)
                
                actions = []
                async with conn.execute(query, params) as cursor:
                    async for row in cursor:
                        action = ActionRecord(
                            id=row['id'],
                            account_phone=row['account_phone'],
                            action_type=row['action_type'],
                            target_group=row['target_group'],
                            target_message_id=row['target_message_id'],
                            content=row['content'],
                            success=bool(row['success']),
                            error_message=row['error_message'],
                            execution_time=row['execution_time'],
                            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None
                        )
                        actions.append(action)
                
                return actions
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка получения действий: {e}")
            return []
    
    # Методы для работы со статистикой
    
    async def update_stats(self, account_phone: str, action_type: str, success: bool, execution_time: float = 0.0):
        """Обновляет статистику аккаунта"""
        try:
            date_str = datetime.now().strftime('%Y-%m-%d')
            conn = await self.get_connection()
            try:
                # Получаем текущую статистику
                async with conn.execute(
                    "SELECT * FROM stats WHERE account_phone = ? AND date = ?",
                    (account_phone, date_str)
                ) as cursor:
                    row = await cursor.fetchone()
                
                if row:
                    # Обновляем существующую запись
                    updates = {
                        'total_execution_time': row['total_execution_time'] + execution_time,
                        'updated_at': datetime.now()
                    }
                    
                    if success:
                        if action_type == 'comment':
                            updates['comments'] = row['comments'] + 1
                        elif action_type == 'reaction':
                            updates['reactions'] = row['reactions'] + 1
                        elif action_type == 'dialogue':
                            updates['dialogues'] = row['dialogues'] + 1
                    else:
                        updates['errors'] = row['errors'] + 1
                    
                    set_clause = ', '.join([f"{k} = ?" for k in updates.keys()])
                    values = list(updates.values()) + [account_phone, date_str]
                    
                    await conn.execute(
                        f"UPDATE stats SET {set_clause} WHERE account_phone = ? AND date = ?",
                        values
                    )
                else:
                    # Создаем новую запись
                    stats = StatsRecord(
                        account_phone=account_phone,
                        date=date_str,
                        total_execution_time=execution_time
                    )
                    
                    if success:
                        if action_type == 'comment':
                            stats.comments = 1
                        elif action_type == 'reaction':
                            stats.reactions = 1
                        elif action_type == 'dialogue':
                            stats.dialogues = 1
                    else:
                        stats.errors = 1
                    
                    await conn.execute("""
                        INSERT INTO stats 
                        (account_phone, date, comments, reactions, dialogues, errors, total_execution_time, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stats.account_phone, stats.date, stats.comments, stats.reactions,
                        stats.dialogues, stats.errors, stats.total_execution_time, stats.updated_at
                    ))
                
                await conn.commit()
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка обновления статистики для {account_phone}: {e}")
    
    async def get_stats(self, account_phone: str = None, days: int = 7) -> List[StatsRecord]:
        """Получает статистику"""
        try:
            since_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            conn = await self.get_connection()
            try:
                query = "SELECT * FROM stats WHERE date >= ?"
                params = [since_date]
                
                if account_phone:
                    query += " AND account_phone = ?"
                    params.append(account_phone)
                
                query += " ORDER BY date DESC"
                
                stats = []
                async with conn.execute(query, params) as cursor:
                    async for row in cursor:
                        stat = StatsRecord(
                            account_phone=row['account_phone'],
                            date=row['date'],
                            comments=row['comments'],
                            reactions=row['reactions'],
                            dialogues=row['dialogues'],
                            errors=row['errors'],
                            total_execution_time=row['total_execution_time'],
                            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                        )
                        stats.append(stat)
                
                return stats
            finally:
                await self.return_connection(conn)
        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            return []
    
    async def close(self):
        """Закрывает базу данных"""
        logger.info("Закрытие базы данных...")
        
        # Закрываем соединения из пула
        for conn in self.connection_pool:
            await conn.close()
        self.connection_pool.clear()
        
        # Закрываем основное соединение
        if self.connection:
            await self.connection.close()
        
        logger.info("База данных закрыта")


# Глобальный экземпляр базы данных
database = AsyncDatabase()
