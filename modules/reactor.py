"""
Модуль для проставления реакций на посты в Telegram группах
Имитирует естественное поведение при реагировании на контент
"""

import asyncio
import random
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

from telethon import TelegramClient
from telethon.errors import FloodWaitError, ReactionInvalidError, ChatWriteForbiddenError
from telethon.tl.types import Message, ReactionEmoji
from telethon.tl.functions.messages import SendReactionRequest

from config import (
    AVAILABLE_REACTIONS,
    DAILY_LIMITS,
    SECURITY_CONFIG
)

# Настройка логирования
logger = logging.getLogger(__name__)


class ReactionTracker:
    """Отслеживает количество реакций для каждого аккаунта"""
    
    def __init__(self):
        self.daily_counts: Dict[str, Dict[str, int]] = {}
        self.last_reset: Dict[str, datetime] = {}
        self.recent_reactions: Dict[str, List[datetime]] = {}  # Для предотвращения спама
    
    def _reset_if_new_day(self, phone: str):
        """Сбрасывает счетчики если наступил новый день"""
        now = datetime.now()
        if phone not in self.last_reset:
            self.last_reset[phone] = now
            self.daily_counts[phone] = {"reactions": 0}
            return
        
        if now.date() > self.last_reset[phone].date():
            self.daily_counts[phone] = {"reactions": 0}
            self.last_reset[phone] = now
            # Очищаем историю реакций
            self.recent_reactions[phone] = []
    
    def can_react(self, phone: str) -> bool:
        """Проверяет можно ли поставить реакцию"""
        self._reset_if_new_day(phone)
        
        if phone not in self.daily_counts:
            self.daily_counts[phone] = {"reactions": 0}
        
        # Проверяем дневной лимит
        if self.daily_counts[phone]["reactions"] >= DAILY_LIMITS["reactions"]:
            return False
        
        # Проверяем частоту реакций (не более 1 реакции в 2 минуты)
        now = datetime.now()
        if phone in self.recent_reactions:
            recent = [r for r in self.recent_reactions[phone] 
                     if now - r < timedelta(minutes=2)]
            if recent:
                return False
        
        return True
    
    def add_reaction(self, phone: str):
        """Увеличивает счетчик реакций"""
        self._reset_if_new_day(phone)
        
        if phone not in self.daily_counts:
            self.daily_counts[phone] = {"reactions": 0}
        
        if phone not in self.recent_reactions:
            self.recent_reactions[phone] = []
        
        now = datetime.now()
        self.daily_counts[phone]["reactions"] += 1
        self.recent_reactions[phone].append(now)
        
        # Оставляем только последние 10 реакций для экономии памяти
        self.recent_reactions[phone] = self.recent_reactions[phone][-10:]
    
    def get_stats(self, phone: str) -> Dict[str, int]:
        """Возвращает статистику для аккаунта"""
        self._reset_if_new_day(phone)
        
        if phone not in self.daily_counts:
            return {"reactions": 0}
        
        return self.daily_counts[phone].copy()


# Глобальный трекер реакций
reaction_tracker = ReactionTracker()


def select_reaction(message_text: str = "") -> str:
    """
    Выбирает подходящую реакцию на основе контента сообщения
    
    Args:
        message_text: Текст сообщения для анализа
        
    Returns:
        str: Выбранная реакция
    """
    # Простая логика выбора реакции на основе ключевых слов
    text_lower = message_text.lower() if message_text else ""
    
    # Позитивные слова -> позитивные реакции
    positive_keywords = [
        "отлично", "супер", "круто", "здорово", "классно", "прекрасно",
        "замечательно", "великолепно", "потрясающе", "восхитительно",
        "успех", "победа", "достижение", "молодец"
    ]
    
    # Эмоциональные слова -> эмоциональные реакции
    emotional_keywords = [
        "любовь", "сердце", "обожаю", "нравится", "восторг",
        "счастье", "радость", "восхищение"
    ]
    
    # Огненные/энергичные слова -> огонь и энергия
    fire_keywords = [
        "огонь", "жара", "энергия", "мощно", "взрыв", "бомба",
        "топ", "лучший", "крутой", "мощный"
    ]
    
    # Проверяем наличие ключевых слов
    if any(keyword in text_lower for keyword in emotional_keywords):
        emotional_reactions = ["❤️", "😍", "🥰"]
        return random.choice(emotional_reactions)
    
    elif any(keyword in text_lower for keyword in fire_keywords):
        fire_reactions = ["🔥", "⚡", "💯", "🚀"]
        return random.choice(fire_reactions)
    
    elif any(keyword in text_lower for keyword in positive_keywords):
        positive_reactions = ["👍", "👏", "🎉", "👌"]
        return random.choice(positive_reactions)
    
    # Если ключевых слов нет, выбираем случайную реакцию
    return random.choice(AVAILABLE_REACTIONS)


async def get_recent_posts_for_reactions(
    client: TelegramClient, 
    group: str, 
    limit: int = 10
) -> List[Message]:
    """
    Получает последние посты подходящие для реакций
    
    Args:
        client: Telegram клиент
        group: Имя группы или ID
        limit: Количество постов для получения
        
    Returns:
        List[Message]: Список сообщений
    """
    try:
        messages = []
        async for message in client.iter_messages(group, limit=limit * 2):  # Берем больше для фильтрации
            # Берем только основные посты (не ответы) с текстом или медиа
            if (message.text or message.media) and not message.reply_to:
                # Проверяем что пост не слишком старый (не более 24 часов)
                if message.date and (datetime.now() - message.date.replace(tzinfo=None)) < timedelta(hours=24):
                    messages.append(message)
                    
                    if len(messages) >= limit:
                        break
        
        return messages
    
    except Exception as e:
        logger.error(f"Ошибка при получении постов из {group}: {e}")
        return []


async def add_reaction_to_post(
    client: TelegramClient,
    phone: str,
    group: str,
    message: Message,
    custom_reaction: Optional[str] = None
) -> bool:
    """
    Добавляет реакцию к посту
    
    Args:
        client: Telegram клиент
        phone: Номер телефона аккаунта
        group: Группа
        message: Сообщение для реакции
        custom_reaction: Пользовательская реакция
        
    Returns:
        bool: True если реакция добавлена успешно
    """
    try:
        # Проверяем лимиты
        if not reaction_tracker.can_react(phone):
            logger.info(f"[{phone}] Достигнут лимит реакций или слишком частые реакции")
            return False
        
        # Выбираем реакцию
        if custom_reaction:
            reaction = custom_reaction
        else:
            reaction = select_reaction(message.text or "")
        
        logger.info(f"[{phone}] Ставлю реакцию '{reaction}' на пост в {group}")
        
        # Небольшая задержка для естественности
        await asyncio.sleep(random.uniform(1, 3))
        
        # Отправляем реакцию
        await client(SendReactionRequest(
            peer=group,
            msg_id=message.id,
            reaction=[ReactionEmoji(emoticon=reaction)]
        ))
        
        # Обновляем счетчик
        reaction_tracker.add_reaction(phone)
        
        logger.info(f"[{phone}] Реакция '{reaction}' добавлена к посту {message.id} в {group}")
        return True
        
    except FloodWaitError as e:
        wait_time = e.seconds + random.randint(30, 120)
        logger.warning(f"[{phone}] FloodWait {e.seconds}с, ждем {wait_time}с")
        await asyncio.sleep(wait_time)
        return False
        
    except ReactionInvalidError as e:
        logger.warning(f"[{phone}] Недопустимая реакция '{reaction}': {e}")
        return False
        
    except (ChatWriteForbiddenError, Exception) as e:
        logger.error(f"[{phone}] Ошибка при добавлении реакции в {group}: {e}")
        return False


async def react_to_random_post(
    client: TelegramClient,
    phone: str,
    groups: List[str]
) -> Optional[Dict[str, Any]]:
    """
    Ставит реакцию на случайный пост в случайной группе
    
    Args:
        client: Telegram клиент
        phone: Номер телефона аккаунта
        groups: Список групп для реакций
        
    Returns:
        Optional[Dict]: Информация о реакции или None
    """
    if not groups:
        logger.warning(f"[{phone}] Список групп пуст")
        return None
    
    # Проверяем лимиты
    if not reaction_tracker.can_react(phone):
        logger.info(f"[{phone}] Достигнут лимит реакций")
        return None
    
    # Выбираем случайную группу
    group = random.choice(groups)
    
    try:
        # Получаем последние посты
        posts = await get_recent_posts_for_reactions(client, group, limit=10)
        
        if not posts:
            logger.warning(f"[{phone}] Нет доступных постов для реакций в {group}")
            return None
        
        # Выбираем случайный пост
        post = random.choice(posts)
        
        # Выбираем реакцию
        reaction = select_reaction(post.text or "")
        
        # Добавляем реакцию
        success = await add_reaction_to_post(client, phone, group, post, reaction)
        
        if success:
            return {
                "phone": phone,
                "group": group,
                "post_id": post.id,
                "reaction": reaction,
                "timestamp": datetime.now()
            }
        
        return None
        
    except Exception as e:
        logger.error(f"[{phone}] Ошибка при добавлении реакции в {group}: {e}")
        return None


def get_reaction_stats(phone: str) -> Dict[str, int]:
    """
    Возвращает статистику реакций для аккаунта
    
    Args:
        phone: Номер телефона аккаунта
        
    Returns:
        Dict[str, int]: Статистика реакций
    """
    return reaction_tracker.get_stats(phone)
