"""
Модуль для анализа контекста постов и генерации релевантных ответов
Определяет тематику, тональность и подбирает подходящие промпты
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ContentType(Enum):
    """Типы контента"""
    TECH = "tech"
    BUSINESS = "business"
    LIFESTYLE = "lifestyle"
    NEWS = "news"
    ENTERTAINMENT = "entertainment"
    EDUCATION = "education"
    UNKNOWN = "unknown"


class Sentiment(Enum):
    """Тональность контента"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    QUESTION = "question"


@dataclass
class ContentAnalysis:
    """Результат анализа контента"""
    content_type: ContentType
    sentiment: Sentiment
    keywords: List[str]
    confidence: float
    suggested_reaction_type: str
    context_prompt: str


class ContextAnalyzer:
    """Анализатор контекста постов"""
    
    def __init__(self):
        self.tech_keywords = [
            "программирование", "код", "разработка", "python", "javascript", "react",
            "api", "база данных", "алгоритм", "фреймворк", "библиотека", "git",
            "docker", "kubernetes", "микросервисы", "архитектура", "devops",
            "машинное обучение", "ai", "нейросеть", "данные", "аналитика"
        ]
        
        self.business_keywords = [
            "бизнес", "стартап", "инвестиции", "прибыль", "доход", "маркетинг",
            "продажи", "клиенты", "стратегия", "менеджмент", "лидерство",
            "предпринимательство", "финансы", "экономика", "рынок", "конкуренция",
            "брендинг", "реклама", "seo", "smm", "конверсия"
        ]
        
        self.lifestyle_keywords = [
            "путешествия", "еда", "рецепт", "фитнес", "здоровье", "спорт",
            "мода", "красота", "дом", "интерьер", "хобби", "отдых",
            "семья", "дети", "отношения", "психология", "саморазвитие",
            "книги", "фильмы", "музыка", "искусство", "фотография"
        ]
        
        self.positive_indicators = [
            "отлично", "супер", "круто", "здорово", "классно", "прекрасно",
            "замечательно", "великолепно", "потрясающе", "восхитительно",
            "успех", "победа", "достижение", "молодец", "браво", "довольны",
            "успешно", "результат", "👍", "❤️", "🔥"
        ]
        
        self.negative_indicators = [
            "плохо", "ужасно", "кошмар", "провал", "неудача", "проблема",
            "ошибка", "катастрофа", "разочарование", "грустно", "печально",
            "злость", "раздражение", "👎", "😢", "😡"
        ]
        
        self.question_indicators = [
            "как", "что", "где", "когда", "почему", "зачем", "кто", "какой",
            "можно ли", "стоит ли", "помогите", "подскажите", "посоветуйте",
            "?", "??", "???"
        ]
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Извлекает ключевые слова из текста"""
        text_lower = text.lower()
        found_keywords = []
        
        all_keywords = (
            self.tech_keywords + 
            self.business_keywords + 
            self.lifestyle_keywords
        )
        
        for keyword in all_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _determine_content_type(self, text: str, keywords: List[str]) -> Tuple[ContentType, float]:
        """Определяет тип контента и уверенность"""
        text_lower = text.lower()
        
        tech_score = sum(1 for kw in keywords if kw in self.tech_keywords)
        business_score = sum(1 for kw in keywords if kw in self.business_keywords)
        lifestyle_score = sum(1 for kw in keywords if kw in self.lifestyle_keywords)
        
        scores = {
            ContentType.TECH: tech_score,
            ContentType.BUSINESS: business_score,
            ContentType.LIFESTYLE: lifestyle_score
        }
        
        # Дополнительные индикаторы
        if any(word in text_lower for word in ["новости", "сегодня", "вчера", "произошло"]):
            scores[ContentType.NEWS] = max(scores.values()) + 1
        
        if any(word in text_lower for word in ["мем", "прикол", "смешно", "юмор", "шутка"]):
            scores[ContentType.ENTERTAINMENT] = max(scores.values()) + 1
        
        if any(word in text_lower for word in ["урок", "обучение", "курс", "изучение", "учеба"]):
            scores[ContentType.EDUCATION] = max(scores.values()) + 1
        
        if not scores or max(scores.values()) == 0:
            return ContentType.UNKNOWN, 0.0
        
        best_type = max(scores, key=scores.get)
        confidence = min(scores[best_type] / 3.0, 1.0)  # Нормализуем до 1.0
        
        return best_type, confidence
    
    def _determine_sentiment(self, text: str) -> Sentiment:
        """Определяет тональность текста"""
        text_lower = text.lower()
        
        # Проверяем на вопрос
        question_count = sum(1 for indicator in self.question_indicators 
                           if indicator in text_lower)
        if question_count > 0:
            return Sentiment.QUESTION
        
        # Считаем позитивные и негативные индикаторы
        positive_count = sum(1 for indicator in self.positive_indicators 
                           if indicator in text_lower)
        negative_count = sum(1 for indicator in self.negative_indicators 
                           if indicator in text_lower)
        
        if positive_count > negative_count:
            return Sentiment.POSITIVE
        elif negative_count > positive_count:
            return Sentiment.NEGATIVE
        else:
            return Sentiment.NEUTRAL
    
    def _suggest_reaction_type(self, content_type: ContentType, sentiment: Sentiment) -> str:
        """Предлагает тип реакции на основе анализа"""
        if sentiment == Sentiment.POSITIVE:
            if content_type == ContentType.TECH:
                return "smart"
            elif content_type == ContentType.BUSINESS:
                return "money"
            else:
                return "positive"
        
        elif sentiment == Sentiment.QUESTION:
            return "smart"
        
        elif content_type == ContentType.ENTERTAINMENT:
            return "fire"
        
        elif content_type == ContentType.LIFESTYLE:
            return "love"
        
        return "positive"  # По умолчанию
    
    def _create_context_prompt(self, analysis: 'ContentAnalysis', text: str) -> str:
        """Создает контекстный промпт для AI"""
        base_context = ""
        
        if analysis.content_type == ContentType.TECH:
            base_context = "Это технический пост. Отвечай как человек, разбирающийся в IT."
        elif analysis.content_type == ContentType.BUSINESS:
            base_context = "Это бизнес-пост. Отвечай как предприниматель или специалист."
        elif analysis.content_type == ContentType.LIFESTYLE:
            base_context = "Это пост о стиле жизни. Отвечай дружелюбно и по-человечески."
        elif analysis.content_type == ContentType.NEWS:
            base_context = "Это новостной пост. Отвечай информативно и взвешенно."
        elif analysis.content_type == ContentType.ENTERTAINMENT:
            base_context = "Это развлекательный пост. Можешь быть более эмоциональным."
        elif analysis.content_type == ContentType.EDUCATION:
            base_context = "Это образовательный пост. Отвечай конструктивно."
        else:
            base_context = "Отвечай естественно и по-человечески."
        
        sentiment_context = ""
        if analysis.sentiment == Sentiment.POSITIVE:
            sentiment_context = " Поддержи позитивный настрой."
        elif analysis.sentiment == Sentiment.NEGATIVE:
            sentiment_context = " Будь тактичным и поддерживающим."
        elif analysis.sentiment == Sentiment.QUESTION:
            sentiment_context = " Это вопрос, можешь выразить заинтересованность."
        
        return base_context + sentiment_context
    
    def analyze_content(self, text: str) -> ContentAnalysis:
        """
        Анализирует контент поста
        
        Args:
            text: Текст поста для анализа
            
        Returns:
            ContentAnalysis: Результат анализа
        """
        if not text or len(text.strip()) < 10:
            return ContentAnalysis(
                content_type=ContentType.UNKNOWN,
                sentiment=Sentiment.NEUTRAL,
                keywords=[],
                confidence=0.0,
                suggested_reaction_type="positive",
                context_prompt="Отвечай естественно и кратко."
            )
        
        # Извлекаем ключевые слова
        keywords = self._extract_keywords(text)
        
        # Определяем тип контента
        content_type, confidence = self._determine_content_type(text, keywords)
        
        # Определяем тональность
        sentiment = self._determine_sentiment(text)
        
        # Предлагаем тип реакции
        reaction_type = self._suggest_reaction_type(content_type, sentiment)
        
        # Создаем анализ
        analysis = ContentAnalysis(
            content_type=content_type,
            sentiment=sentiment,
            keywords=keywords,
            confidence=confidence,
            suggested_reaction_type=reaction_type,
            context_prompt=""
        )
        
        # Создаем контекстный промпт
        analysis.context_prompt = self._create_context_prompt(analysis, text)
        
        logger.debug(
            f"Анализ контента: тип={content_type.value}, "
            f"тональность={sentiment.value}, уверенность={confidence:.2f}"
        )
        
        return analysis
    
    def get_contextual_templates(self, analysis: ContentAnalysis) -> Dict[str, List[str]]:
        """Возвращает контекстные шаблоны для Spintax fallback"""
        templates = {
            "comments": [],
            "replies": []
        }
        
        if analysis.content_type == ContentType.TECH:
            templates["comments"] = [
                "{Интересное|Полезное|Крутое} {решение|объяснение|описание}! {Спасибо|Благодарю}.",
                "{Актуально|Важно} для {разработчиков|программистов}. {Сохраню|Изучу}!",
                "{Хорошая|Качественная} {статья|информация}. {Все понятно|Доступно}."
            ]
            templates["replies"] = [
                "{Согласен|Поддерживаю}! {У меня похожий опыт|Тоже так думаю}.",
                "{Правильно|Верно}! {Это основа|Это важно}.",
                "{Интересная мысль|Хороший подход}! {Попробую|Возьму на заметку}."
            ]
        
        elif analysis.content_type == ContentType.BUSINESS:
            templates["comments"] = [
                "{Дельный|Полезный} совет! {Применю|Возьму на заметку}.",
                "{Согласен|Поддерживаю} с {подходом|стратегией}. {Работает|Проверено}!",
                "{Интересный|Полезный} {кейс|опыт}. {Спасибо за детали|Благодарю}!"
            ]
            templates["replies"] = [
                "{Полностью согласен|Разделяю мнение}! {Сам так работаю|Похожий опыт}.",
                "{Правильная|Верная} {стратегия|мысль}! {Результат будет|Сработает}.",
                "{А что думаешь|Как считаешь} насчет {альтернативы|другого подхода}?"
            ]
        
        else:  # Универсальные шаблоны
            templates["comments"] = [
                "{Интересно|Познавательно}! {Спасибо|Благодарю}.",
                "{Хорошо|Здорово} {написано|рассказано}! {Понравилось|Полезно}.",
                "{Согласен|Поддерживаю}! {Актуальная тема|Важный вопрос}."
            ]
            templates["replies"] = [
                "{Согласен|Поддерживаю}! {Хорошая мысль|Правильно}.",
                "{Интересно|Любопытно}! {Не думал об этом|Стоит подумать}.",
                "{Спасибо за ответ|Благодарю}! {Полезно|Понятно}."
            ]
        
        return templates


# Глобальный экземпляр анализатора
context_analyzer = ContextAnalyzer()
