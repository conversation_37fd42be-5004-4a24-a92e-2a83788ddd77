"""
AI модуль для генерации текстов через различные нейросети
Поддерживает DeepSeek, Groq, OpenAI и другие API с fallback на Spintax
"""

import asyncio
import logging
import random
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum

import httpx
from openai import AsyncOpenAI

from spintax import generate_comment, generate_reply

# Настройка логирования
logger = logging.getLogger(__name__)


class AIProvider(Enum):
    """Поддерживаемые AI провайдеры"""
    DEEPSEEK = "deepseek"
    GROQ = "groq"
    OPENAI = "openai"
    OLLAMA = "ollama"
    SPINTAX = "spintax"  # Fallback


@dataclass
class AIConfig:
    """Конфигурация AI провайдера"""
    provider: AIProvider
    api_key: Optional[str]
    base_url: Optional[str]
    model: str
    max_tokens: int = 150
    temperature: float = 0.8
    timeout: int = 30


class AITextGenerator:
    """Генератор текстов через различные AI API"""
    
    def __init__(self):
        self.providers: Dict[AIProvider, AIConfig] = {}
        self.clients: Dict[AIProvider, Any] = {}
        self.fallback_enabled = True
        self.request_counts: Dict[AIProvider, int] = {}
        self.last_request_time: Dict[AIProvider, float] = {}
        
        # Инициализируем провайдеры
        self._init_providers()
    
    def _init_providers(self):
        """Инициализирует доступных провайдеров"""
        
        # DeepSeek (бесплатный API)
        self.providers[AIProvider.DEEPSEEK] = AIConfig(
            provider=AIProvider.DEEPSEEK,
            api_key="sk-your-deepseek-key",  # Замените на реальный ключ
            base_url="https://api.deepseek.com/v1",
            model="deepseek-chat",
            max_tokens=100,
            temperature=0.7
        )
        
        # Groq (бесплатный API)
        self.providers[AIProvider.GROQ] = AIConfig(
            provider=AIProvider.GROQ,
            api_key="gsk_your-groq-key",  # Замените на реальный ключ
            base_url="https://api.groq.com/openai/v1",
            model="llama3-8b-8192",
            max_tokens=100,
            temperature=0.8
        )
        
        # OpenAI (платный, для примера)
        self.providers[AIProvider.OPENAI] = AIConfig(
            provider=AIProvider.OPENAI,
            api_key="sk-your-openai-key",  # Замените на реальный ключ
            base_url="https://api.openai.com/v1",
            model="gpt-3.5-turbo",
            max_tokens=100,
            temperature=0.7
        )
        
        # Создаем клиентов
        for provider, config in self.providers.items():
            if config.api_key and config.api_key != "sk-your-deepseek-key":
                try:
                    self.clients[provider] = AsyncOpenAI(
                        api_key=config.api_key,
                        base_url=config.base_url
                    )
                    logger.info(f"Инициализирован клиент {provider.value}")
                except Exception as e:
                    logger.error(f"Ошибка инициализации {provider.value}: {e}")
    
    def _get_available_provider(self) -> Optional[AIProvider]:
        """Возвращает доступного провайдера с учетом лимитов"""
        available_providers = []
        
        for provider in [AIProvider.DEEPSEEK, AIProvider.GROQ, AIProvider.OPENAI]:
            if provider in self.clients:
                # Проверяем лимиты запросов
                current_time = time.time()
                last_request = self.last_request_time.get(provider, 0)
                
                # Минимальная задержка между запросами (1 секунда)
                if current_time - last_request >= 1.0:
                    available_providers.append(provider)
        
        return random.choice(available_providers) if available_providers else None
    
    def _create_comment_prompt(self, post_text: str, group_context: str = "") -> str:
        """Создает промпт для генерации комментария"""
        base_prompt = """Ты пишешь короткий комментарий к посту в Telegram группе. 
Комментарий должен быть:
- Естественным и человечным
- Коротким (1-2 предложения, максимум 50 слов)
- На русском языке
- Позитивным или нейтральным
- Без эмодзи и специальных символов

Пост: "{post_text}"

Напиши только комментарий, без дополнительных объяснений:"""
        
        return base_prompt.format(post_text=post_text[:200])  # Ограничиваем длину поста
    
    def _create_reply_prompt(self, original_comment: str, post_context: str = "") -> str:
        """Создает промпт для генерации ответа в диалоге"""
        base_prompt = """Ты отвечаешь на комментарий в Telegram группе. 
Ответ должен быть:
- Естественным и дружелюбным
- Коротким (1-2 предложения, максимум 40 слов)
- На русском языке
- Логически связанным с исходным комментарием
- Без эмодзи и специальных символов

Исходный комментарий: "{original_comment}"

Напиши только ответ, без дополнительных объяснений:"""
        
        return base_prompt.format(original_comment=original_comment[:150])
    
    async def _make_ai_request(
        self, 
        provider: AIProvider, 
        prompt: str
    ) -> Optional[str]:
        """Выполняет запрос к AI API"""
        if provider not in self.clients:
            return None
        
        config = self.providers[provider]
        client = self.clients[provider]
        
        try:
            # Обновляем время последнего запроса
            self.last_request_time[provider] = time.time()
            self.request_counts[provider] = self.request_counts.get(provider, 0) + 1
            
            response = await client.chat.completions.create(
                model=config.model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=config.max_tokens,
                temperature=config.temperature,
                timeout=config.timeout
            )
            
            generated_text = response.choices[0].message.content.strip()
            
            # Базовая валидация
            if len(generated_text) > 200 or len(generated_text) < 5:
                logger.warning(f"Неподходящая длина текста от {provider.value}: {len(generated_text)}")
                return None
            
            logger.info(f"Успешная генерация от {provider.value}: '{generated_text[:50]}...'")
            return generated_text
            
        except Exception as e:
            logger.error(f"Ошибка запроса к {provider.value}: {e}")
            return None
    
    async def generate_comment(
        self, 
        post_text: str, 
        group_context: str = "",
        fallback_templates: Optional[List[str]] = None
    ) -> str:
        """
        Генерирует комментарий к посту
        
        Args:
            post_text: Текст поста
            group_context: Контекст группы
            fallback_templates: Шаблоны для fallback
            
        Returns:
            str: Сгенерированный комментарий
        """
        # Пробуем AI генерацию
        provider = self._get_available_provider()
        
        if provider:
            prompt = self._create_comment_prompt(post_text, group_context)
            result = await self._make_ai_request(provider, prompt)
            
            if result:
                return result
        
        # Fallback на Spintax
        if self.fallback_enabled:
            logger.info("Используем Spintax fallback для комментария")
            from config import COMMENT_TEMPLATES
            templates = fallback_templates or COMMENT_TEMPLATES
            return generate_comment(templates)
        
        return "Интересно!"
    
    async def generate_reply(
        self, 
        original_comment: str, 
        post_context: str = "",
        fallback_templates: Optional[List[str]] = None
    ) -> str:
        """
        Генерирует ответ на комментарий
        
        Args:
            original_comment: Исходный комментарий
            post_context: Контекст поста
            fallback_templates: Шаблоны для fallback
            
        Returns:
            str: Сгенерированный ответ
        """
        # Пробуем AI генерацию
        provider = self._get_available_provider()
        
        if provider:
            prompt = self._create_reply_prompt(original_comment, post_context)
            result = await self._make_ai_request(provider, prompt)
            
            if result:
                return result
        
        # Fallback на Spintax
        if self.fallback_enabled:
            logger.info("Используем Spintax fallback для ответа")
            from config import REPLY_TEMPLATES
            templates = fallback_templates or REPLY_TEMPLATES
            return generate_reply(templates)
        
        return "Согласен!"
    
    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику использования AI"""
        return {
            "providers": {
                provider.value: {
                    "available": provider in self.clients,
                    "requests": self.request_counts.get(provider, 0),
                    "last_request": self.last_request_time.get(provider, 0)
                }
                for provider in AIProvider if provider != AIProvider.SPINTAX
            },
            "fallback_enabled": self.fallback_enabled
        }
    
    def set_fallback_enabled(self, enabled: bool):
        """Включает/выключает fallback на Spintax"""
        self.fallback_enabled = enabled
        logger.info(f"Fallback на Spintax: {'включен' if enabled else 'выключен'}")


# Глобальный экземпляр генератора
ai_generator = AITextGenerator()
