"""
Система мониторинга производительности и здоровья фермы
Отслеживает метрики, производительность и создает алерты
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import psutil

logger = logging.getLogger(__name__)


@dataclass
class Metric:
    """Метрика системы"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class Alert:
    """Алерт системы"""
    id: str
    level: str  # info, warning, error, critical
    message: str
    metric_name: str
    threshold: float
    current_value: float
    timestamp: datetime
    resolved: bool = False


class MetricsCollector:
    """Сборщик метрик"""
    
    def __init__(self, max_metrics: int = 10000):
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_metrics))
        self.collection_interval = 30  # секунд
        self.running = False
        self.collection_task: Optional[asyncio.Task] = None
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Записывает метрику"""
        metric = Metric(
            name=name,
            value=value,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        self.metrics[name].append(metric)
    
    def get_metrics(self, name: str, since: datetime = None) -> List[Metric]:
        """Получает метрики по имени"""
        metrics = list(self.metrics.get(name, []))
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        return metrics
    
    def get_latest_metric(self, name: str) -> Optional[Metric]:
        """Получает последнюю метрику"""
        metrics = self.metrics.get(name)
        return metrics[-1] if metrics else None
    
    def get_average(self, name: str, minutes: int = 5) -> Optional[float]:
        """Получает среднее значение за период"""
        since = datetime.now() - timedelta(minutes=minutes)
        metrics = self.get_metrics(name, since)
        
        if not metrics:
            return None
        
        return sum(m.value for m in metrics) / len(metrics)
    
    async def collect_system_metrics(self):
        """Собирает системные метрики"""
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_metric("system.cpu.percent", cpu_percent)
            
            # Memory
            memory = psutil.virtual_memory()
            self.record_metric("system.memory.percent", memory.percent)
            self.record_metric("system.memory.used_gb", memory.used / (1024**3))
            self.record_metric("system.memory.available_gb", memory.available / (1024**3))
            
            # Disk
            disk = psutil.disk_usage('/')
            self.record_metric("system.disk.percent", disk.percent)
            self.record_metric("system.disk.free_gb", disk.free / (1024**3))
            
            # Network
            network = psutil.net_io_counters()
            self.record_metric("system.network.bytes_sent", network.bytes_sent)
            self.record_metric("system.network.bytes_recv", network.bytes_recv)
            
            # Process info
            process = psutil.Process()
            self.record_metric("process.cpu.percent", process.cpu_percent())
            self.record_metric("process.memory.mb", process.memory_info().rss / (1024**2))
            self.record_metric("process.threads", process.num_threads())
            
        except Exception as e:
            logger.error(f"Ошибка сбора системных метрик: {e}")
    
    async def collection_loop(self):
        """Цикл сбора метрик"""
        while self.running:
            try:
                await self.collect_system_metrics()
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Ошибка в цикле сбора метрик: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def start(self):
        """Запускает сбор метрик"""
        if self.running:
            return
        
        self.running = True
        self.collection_task = asyncio.create_task(self.collection_loop())
        logger.info("Сборщик метрик запущен")
    
    async def stop(self):
        """Останавливает сбор метрик"""
        if not self.running:
            return
        
        self.running = False
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Сборщик метрик остановлен")


class AlertManager:
    """Менеджер алертов"""
    
    def __init__(self):
        self.alerts: List[Alert] = []
        self.rules: List[Dict[str, Any]] = []
        self.callbacks: List[Callable] = []
        self.check_interval = 60  # секунд
        self.running = False
        self.check_task: Optional[asyncio.Task] = None
    
    def add_rule(self, metric_name: str, threshold: float, condition: str = "greater", 
                 level: str = "warning", message: str = None):
        """Добавляет правило алерта"""
        rule = {
            "metric_name": metric_name,
            "threshold": threshold,
            "condition": condition,  # greater, less, equal
            "level": level,
            "message": message or f"{metric_name} {condition} {threshold}"
        }
        self.rules.append(rule)
        logger.info(f"Добавлено правило алерта: {rule}")
    
    def add_callback(self, callback: Callable):
        """Добавляет callback для алертов"""
        self.callbacks.append(callback)
    
    def create_alert(self, rule: Dict[str, Any], current_value: float) -> Alert:
        """Создает алерт"""
        alert_id = f"{rule['metric_name']}_{int(time.time())}"
        
        alert = Alert(
            id=alert_id,
            level=rule["level"],
            message=rule["message"],
            metric_name=rule["metric_name"],
            threshold=rule["threshold"],
            current_value=current_value,
            timestamp=datetime.now()
        )
        
        return alert
    
    async def check_rules(self, metrics_collector: MetricsCollector):
        """Проверяет правила алертов"""
        for rule in self.rules:
            try:
                metric = metrics_collector.get_latest_metric(rule["metric_name"])
                if not metric:
                    continue
                
                should_alert = False
                current_value = metric.value
                threshold = rule["threshold"]
                condition = rule["condition"]
                
                if condition == "greater" and current_value > threshold:
                    should_alert = True
                elif condition == "less" and current_value < threshold:
                    should_alert = True
                elif condition == "equal" and abs(current_value - threshold) < 0.01:
                    should_alert = True
                
                if should_alert:
                    # Проверяем, нет ли уже активного алерта
                    existing_alert = next(
                        (a for a in self.alerts 
                         if a.metric_name == rule["metric_name"] and not a.resolved),
                        None
                    )
                    
                    if not existing_alert:
                        alert = self.create_alert(rule, current_value)
                        self.alerts.append(alert)
                        
                        # Вызываем callbacks
                        for callback in self.callbacks:
                            try:
                                await callback(alert)
                            except Exception as e:
                                logger.error(f"Ошибка в callback алерта: {e}")
                        
                        logger.warning(f"🚨 АЛЕРТ: {alert.message} (значение: {current_value})")
                
            except Exception as e:
                logger.error(f"Ошибка проверки правила {rule}: {e}")
    
    async def check_loop(self, metrics_collector: MetricsCollector):
        """Цикл проверки алертов"""
        while self.running:
            try:
                await self.check_rules(metrics_collector)
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Ошибка в цикле проверки алертов: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def start(self, metrics_collector: MetricsCollector):
        """Запускает менеджер алертов"""
        if self.running:
            return
        
        self.running = True
        self.check_task = asyncio.create_task(self.check_loop(metrics_collector))
        logger.info("Менеджер алертов запущен")
    
    async def stop(self):
        """Останавливает менеджер алертов"""
        if not self.running:
            return
        
        self.running = False
        if self.check_task:
            self.check_task.cancel()
            try:
                await self.check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Менеджер алертов остановлен")
    
    def get_active_alerts(self) -> List[Alert]:
        """Возвращает активные алерты"""
        return [a for a in self.alerts if not a.resolved]
    
    def resolve_alert(self, alert_id: str):
        """Помечает алерт как решенный"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                logger.info(f"Алерт {alert_id} помечен как решенный")
                break


class PerformanceDashboard:
    """Дашборд производительности"""
    
    def __init__(self, metrics_collector: MetricsCollector, alert_manager: AlertManager):
        self.metrics_collector = metrics_collector
        self.alert_manager = alert_manager
    
    def get_system_health(self) -> Dict[str, Any]:
        """Возвращает общее состояние системы"""
        health = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "metrics": {},
            "alerts": len(self.alert_manager.get_active_alerts())
        }
        
        # Основные метрики
        key_metrics = [
            "system.cpu.percent",
            "system.memory.percent", 
            "system.disk.percent",
            "process.memory.mb"
        ]
        
        for metric_name in key_metrics:
            metric = self.metrics_collector.get_latest_metric(metric_name)
            if metric:
                health["metrics"][metric_name] = {
                    "current": metric.value,
                    "average_5m": self.metrics_collector.get_average(metric_name, 5)
                }
        
        # Определяем общий статус
        cpu = health["metrics"].get("system.cpu.percent", {}).get("current", 0)
        memory = health["metrics"].get("system.memory.percent", {}).get("current", 0)
        
        if cpu > 90 or memory > 90 or health["alerts"] > 0:
            health["status"] = "critical"
        elif cpu > 70 or memory > 70:
            health["status"] = "warning"
        
        return health
    
    def get_farm_metrics(self) -> Dict[str, Any]:
        """Возвращает метрики фермы"""
        farm_metrics = {}
        
        # Метрики активности
        activity_metrics = [
            "farm.comments.total",
            "farm.reactions.total", 
            "farm.dialogues.total",
            "farm.errors.total",
            "farm.accounts.active",
            "farm.queue.size"
        ]
        
        for metric_name in activity_metrics:
            metric = self.metrics_collector.get_latest_metric(metric_name)
            if metric:
                farm_metrics[metric_name] = {
                    "current": metric.value,
                    "average_1h": self.metrics_collector.get_average(metric_name, 60)
                }
        
        return farm_metrics
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Генерирует отчет о производительности"""
        return {
            "system_health": self.get_system_health(),
            "farm_metrics": self.get_farm_metrics(),
            "active_alerts": [
                {
                    "id": alert.id,
                    "level": alert.level,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat()
                }
                for alert in self.alert_manager.get_active_alerts()
            ],
            "uptime": self._get_uptime()
        }
    
    def _get_uptime(self) -> Dict[str, Any]:
        """Возвращает время работы"""
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        
        return {
            "system_uptime_hours": uptime.total_seconds() / 3600,
            "boot_time": boot_time.isoformat()
        }


class FarmMonitor:
    """Главный монитор фермы"""
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        self.dashboard = PerformanceDashboard(self.metrics_collector, self.alert_manager)
        
        # Настраиваем стандартные правила алертов
        self._setup_default_alerts()
    
    def _setup_default_alerts(self):
        """Настраивает стандартные алерты"""
        # Системные алерты
        self.alert_manager.add_rule("system.cpu.percent", 85, "greater", "warning", "Высокая загрузка CPU")
        self.alert_manager.add_rule("system.cpu.percent", 95, "greater", "critical", "Критическая загрузка CPU")
        self.alert_manager.add_rule("system.memory.percent", 85, "greater", "warning", "Высокое использование памяти")
        self.alert_manager.add_rule("system.memory.percent", 95, "greater", "critical", "Критическое использование памяти")
        self.alert_manager.add_rule("system.disk.percent", 90, "greater", "warning", "Мало места на диске")
        
        # Алерты фермы
        self.alert_manager.add_rule("farm.errors.rate", 0.3, "greater", "warning", "Высокий процент ошибок")
        self.alert_manager.add_rule("farm.accounts.active", 5, "less", "warning", "Мало активных аккаунтов")
    
    def record_farm_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """Записывает метрику фермы"""
        self.metrics_collector.record_metric(f"farm.{name}", value, tags)
    
    async def start(self):
        """Запускает мониторинг"""
        await self.metrics_collector.start()
        await self.alert_manager.start(self.metrics_collector)
        logger.info("🔍 Система мониторинга запущена")
    
    async def stop(self):
        """Останавливает мониторинг"""
        await self.alert_manager.stop()
        await self.metrics_collector.stop()
        logger.info("Система мониторинга остановлена")
    
    def get_health_report(self) -> Dict[str, Any]:
        """Возвращает отчет о здоровье системы"""
        return self.dashboard.get_performance_report()


# Глобальный экземпляр монитора
farm_monitor = FarmMonitor()
