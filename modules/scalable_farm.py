"""
Масштабируемая архитектура для управления большим количеством аккаунтов
Включает пулы подключений, очереди задач и балансировку нагрузки
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import psutil
from collections import defaultdict, deque

from telethon import TelegramClient

# Настройка логирования
logger = logging.getLogger(__name__)


class TaskType(Enum):
    """Типы задач"""
    COMMENT = "comment"
    REACTION = "reaction"
    DIALOGUE = "dialogue"


class AccountStatus(Enum):
    """Статусы аккаунтов"""
    ACTIVE = "active"
    IDLE = "idle"
    COOLDOWN = "cooldown"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class Task:
    """Задача для выполнения"""
    task_id: str
    task_type: TaskType
    account_phone: str
    target_group: str
    priority: int = 1
    created_at: datetime = field(default_factory=datetime.now)
    attempts: int = 0
    max_attempts: int = 3
    data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AccountInfo:
    """Информация об аккаунте"""
    phone: str
    status: AccountStatus
    last_activity: Optional[datetime] = None
    error_count: int = 0
    total_actions: int = 0
    proxy_group: Optional[str] = None
    current_task: Optional[Task] = None
    cooldown_until: Optional[datetime] = None


class TaskQueue:
    """Очередь задач с приоритетами"""
    
    def __init__(self, max_size: int = 10000):
        self.queues: Dict[int, deque] = defaultdict(deque)
        self.max_size = max_size
        self.total_size = 0
    
    def put(self, task: Task) -> bool:
        """Добавляет задачу в очередь"""
        if self.total_size >= self.max_size:
            logger.warning("Очередь задач переполнена")
            return False
        
        self.queues[task.priority].append(task)
        self.total_size += 1
        return True
    
    def get(self) -> Optional[Task]:
        """Получает задачу с наивысшим приоритетом"""
        for priority in sorted(self.queues.keys(), reverse=True):
            if self.queues[priority]:
                task = self.queues[priority].popleft()
                self.total_size -= 1
                return task
        return None
    
    def size(self) -> int:
        """Возвращает размер очереди"""
        return self.total_size
    
    def clear(self):
        """Очищает очередь"""
        self.queues.clear()
        self.total_size = 0


class ConnectionPool:
    """Пул подключений для аккаунтов"""
    
    def __init__(self, max_connections: int = 50):
        self.max_connections = max_connections
        self.active_connections: Dict[str, TelegramClient] = {}
        self.connection_usage: Dict[str, datetime] = {}
        self.connection_lock = asyncio.Lock()
    
    async def get_connection(self, phone: str, client_factory) -> Optional[TelegramClient]:
        """Получает подключение для аккаунта"""
        async with self.connection_lock:
            # Если подключение уже существует
            if phone in self.active_connections:
                self.connection_usage[phone] = datetime.now()
                return self.active_connections[phone]
            
            # Если достигнут лимит подключений, освобождаем старые
            if len(self.active_connections) >= self.max_connections:
                await self._cleanup_old_connections()
            
            # Создаем новое подключение
            try:
                client = await client_factory(phone)
                if client:
                    self.active_connections[phone] = client
                    self.connection_usage[phone] = datetime.now()
                    logger.info(f"Создано подключение для {phone}")
                    return client
            except Exception as e:
                logger.error(f"Ошибка создания подключения для {phone}: {e}")
            
            return None
    
    async def _cleanup_old_connections(self):
        """Очищает старые неиспользуемые подключения"""
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=30)  # 30 минут неактивности
        
        to_remove = []
        for phone, last_used in self.connection_usage.items():
            if last_used < cutoff_time:
                to_remove.append(phone)
        
        for phone in to_remove:
            await self.release_connection(phone)
    
    async def release_connection(self, phone: str):
        """Освобождает подключение"""
        if phone in self.active_connections:
            try:
                await self.active_connections[phone].disconnect()
            except:
                pass
            
            del self.active_connections[phone]
            del self.connection_usage[phone]
            logger.info(f"Освобождено подключение для {phone}")
    
    async def cleanup_all(self):
        """Закрывает все подключения"""
        for phone in list(self.active_connections.keys()):
            await self.release_connection(phone)


class LoadBalancer:
    """Балансировщик нагрузки между аккаунтами"""
    
    def __init__(self):
        self.account_loads: Dict[str, float] = defaultdict(float)
        self.proxy_groups: Dict[str, List[str]] = defaultdict(list)
        self.group_loads: Dict[str, float] = defaultdict(float)
    
    def register_account(self, phone: str, proxy_group: str):
        """Регистрирует аккаунт в балансировщике"""
        self.account_loads[phone] = 0.0
        self.proxy_groups[proxy_group].append(phone)
    
    def get_best_account(self, exclude_phones: Set[str] = None) -> Optional[str]:
        """Возвращает аккаунт с наименьшей нагрузкой"""
        exclude_phones = exclude_phones or set()
        
        available_accounts = [
            phone for phone in self.account_loads.keys()
            if phone not in exclude_phones
        ]
        
        if not available_accounts:
            return None
        
        # Выбираем аккаунт с минимальной нагрузкой
        return min(available_accounts, key=lambda p: self.account_loads[p])
    
    def get_best_proxy_group(self) -> Optional[str]:
        """Возвращает группу прокси с наименьшей нагрузкой"""
        if not self.proxy_groups:
            return None
        
        return min(self.proxy_groups.keys(), key=lambda g: self.group_loads[g])
    
    def add_load(self, phone: str, load: float):
        """Добавляет нагрузку к аккаунту"""
        self.account_loads[phone] += load
        
        # Находим группу прокси для аккаунта
        for group, phones in self.proxy_groups.items():
            if phone in phones:
                self.group_loads[group] += load
                break
    
    def decay_loads(self, decay_factor: float = 0.95):
        """Уменьшает нагрузку со временем"""
        for phone in self.account_loads:
            self.account_loads[phone] *= decay_factor
        
        for group in self.group_loads:
            self.group_loads[group] *= decay_factor


class PerformanceMonitor:
    """Монитор производительности системы"""
    
    def __init__(self):
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.start_time = datetime.now()
    
    def record_metric(self, name: str, value: float):
        """Записывает метрику"""
        self.metrics[name].append((datetime.now(), value))
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Возвращает системные метрики"""
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "network_io": psutil.net_io_counters()._asdict(),
            "uptime": (datetime.now() - self.start_time).total_seconds()
        }
    
    def get_farm_metrics(self) -> Dict[str, Any]:
        """Возвращает метрики фермы"""
        metrics = {}
        
        for name, values in self.metrics.items():
            if values:
                recent_values = [v[1] for v in values if v[0] > datetime.now() - timedelta(minutes=5)]
                if recent_values:
                    metrics[name] = {
                        "current": recent_values[-1],
                        "average": sum(recent_values) / len(recent_values),
                        "min": min(recent_values),
                        "max": max(recent_values)
                    }
        
        return metrics


class ScalableFarmManager:
    """Масштабируемый менеджер фермы"""
    
    def __init__(self, max_connections: int = 100):
        self.task_queue = TaskQueue()
        self.connection_pool = ConnectionPool(max_connections)
        self.load_balancer = LoadBalancer()
        self.performance_monitor = PerformanceMonitor()
        
        self.accounts: Dict[str, AccountInfo] = {}
        self.running = False
        self.worker_tasks: List[asyncio.Task] = []
        self.num_workers = min(20, max_connections // 5)  # 20% от максимальных подключений
    
    def register_account(self, phone: str, proxy_group: str = "default"):
        """Регистрирует аккаунт в системе"""
        self.accounts[phone] = AccountInfo(
            phone=phone,
            status=AccountStatus.IDLE,
            proxy_group=proxy_group
        )
        self.load_balancer.register_account(phone, proxy_group)
        logger.info(f"Зарегистрирован аккаунт {phone} в группе {proxy_group}")
    
    def add_task(self, task: Task) -> bool:
        """Добавляет задачу в очередь"""
        if task.account_phone not in self.accounts:
            logger.error(f"Аккаунт {task.account_phone} не зарегистрирован")
            return False
        
        account = self.accounts[task.account_phone]
        if account.status == AccountStatus.DISABLED:
            logger.warning(f"Аккаунт {task.account_phone} отключен")
            return False
        
        return self.task_queue.put(task)
    
    async def _worker(self, worker_id: int):
        """Воркер для обработки задач"""
        logger.info(f"Запущен воркер {worker_id}")
        
        while self.running:
            try:
                # Получаем задачу из очереди
                task = self.task_queue.get()
                if not task:
                    await asyncio.sleep(1)
                    continue
                
                # Проверяем доступность аккаунта
                account = self.accounts.get(task.account_phone)
                if not account or account.status not in [AccountStatus.IDLE, AccountStatus.ACTIVE]:
                    # Возвращаем задачу в очередь если аккаунт недоступен
                    if task.attempts < task.max_attempts:
                        task.attempts += 1
                        self.task_queue.put(task)
                    continue
                
                # Выполняем задачу
                await self._execute_task(task, worker_id)
                
            except Exception as e:
                logger.error(f"Ошибка в воркере {worker_id}: {e}")
                await asyncio.sleep(5)
    
    async def _execute_task(self, task: Task, worker_id: int):
        """Выполняет задачу"""
        account = self.accounts[task.account_phone]
        
        try:
            # Устанавливаем статус
            account.status = AccountStatus.ACTIVE
            account.current_task = task
            
            start_time = time.time()
            
            # Здесь будет логика выполнения задачи
            # В зависимости от типа задачи вызываем соответствующие функции
            success = await self._perform_task_action(task)
            
            execution_time = time.time() - start_time
            
            if success:
                account.total_actions += 1
                account.last_activity = datetime.now()
                account.error_count = 0
                
                # Добавляем нагрузку
                self.load_balancer.add_load(task.account_phone, 1.0)
                
                # Записываем метрики
                self.performance_monitor.record_metric(f"task_{task.task_type.value}_time", execution_time)
                self.performance_monitor.record_metric("tasks_completed", 1)
                
                logger.info(f"Воркер {worker_id} выполнил задачу {task.task_type.value} для {task.account_phone}")
            else:
                account.error_count += 1
                if account.error_count >= 3:
                    account.status = AccountStatus.ERROR
                    account.cooldown_until = datetime.now() + timedelta(hours=1)
                
                self.performance_monitor.record_metric("tasks_failed", 1)
        
        except Exception as e:
            logger.error(f"Ошибка выполнения задачи {task.task_id}: {e}")
            account.error_count += 1
            account.status = AccountStatus.ERROR
        
        finally:
            # Сбрасываем статус
            if account.status == AccountStatus.ACTIVE:
                account.status = AccountStatus.IDLE
            account.current_task = None
    
    async def _perform_task_action(self, task: Task) -> bool:
        """Выполняет действие задачи"""
        # Здесь должна быть интеграция с существующими модулями
        # commenter, reactor, dialogue
        
        # Имитация выполнения
        await asyncio.sleep(random.uniform(1, 3))
        return random.random() > 0.1  # 90% успеха
    
    async def start(self):
        """Запускает масштабируемую ферму"""
        if self.running:
            return
        
        self.running = True
        logger.info(f"Запуск масштабируемой фермы с {self.num_workers} воркерами")
        
        # Запускаем воркеров
        for i in range(self.num_workers):
            task = asyncio.create_task(self._worker(i))
            self.worker_tasks.append(task)
        
        # Запускаем мониторинг
        asyncio.create_task(self._monitoring_loop())
        asyncio.create_task(self._load_decay_loop())
    
    async def stop(self):
        """Останавливает ферму"""
        if not self.running:
            return
        
        self.running = False
        logger.info("Остановка масштабируемой фермы")
        
        # Останавливаем воркеров
        for task in self.worker_tasks:
            task.cancel()
        
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
        
        # Закрываем подключения
        await self.connection_pool.cleanup_all()
    
    async def _monitoring_loop(self):
        """Цикл мониторинга"""
        while self.running:
            try:
                # Записываем системные метрики
                system_metrics = self.performance_monitor.get_system_metrics()
                for name, value in system_metrics.items():
                    if isinstance(value, (int, float)):
                        self.performance_monitor.record_metric(f"system_{name}", value)
                
                # Записываем метрики очереди
                self.performance_monitor.record_metric("queue_size", self.task_queue.size())
                self.performance_monitor.record_metric("active_connections", len(self.connection_pool.active_connections))
                
                await asyncio.sleep(30)  # Каждые 30 секунд
                
            except Exception as e:
                logger.error(f"Ошибка в мониторинге: {e}")
                await asyncio.sleep(60)
    
    async def _load_decay_loop(self):
        """Цикл уменьшения нагрузки"""
        while self.running:
            try:
                self.load_balancer.decay_loads()
                await asyncio.sleep(300)  # Каждые 5 минут
            except Exception as e:
                logger.error(f"Ошибка в decay loop: {e}")
                await asyncio.sleep(300)
    
    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику системы"""
        active_accounts = sum(1 for acc in self.accounts.values() if acc.status == AccountStatus.ACTIVE)
        idle_accounts = sum(1 for acc in self.accounts.values() if acc.status == AccountStatus.IDLE)
        error_accounts = sum(1 for acc in self.accounts.values() if acc.status == AccountStatus.ERROR)
        
        return {
            "total_accounts": len(self.accounts),
            "active_accounts": active_accounts,
            "idle_accounts": idle_accounts,
            "error_accounts": error_accounts,
            "queue_size": self.task_queue.size(),
            "active_connections": len(self.connection_pool.active_connections),
            "workers": len(self.worker_tasks),
            "system_metrics": self.performance_monitor.get_system_metrics(),
            "farm_metrics": self.performance_monitor.get_farm_metrics()
        }
