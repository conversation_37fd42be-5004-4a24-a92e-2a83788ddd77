"""
Асинхронный пул соединений для оптимизации производительности Telegram фермы
Устраняет блокирующие операции и оптимизирует управление ресурсами
"""

import asyncio
import logging
import weakref
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import psutil
from collections import defaultdict, deque

from telethon import TelegramClient
from telethon.errors import FloodWaitError, AuthKeyUnregisteredError
import socks

from config import TELEGRAM_CONFIG, SECURITY_CONFIG

logger = logging.getLogger(__name__)


@dataclass
class ConnectionMetrics:
    """Метрики соединения"""
    created_at: datetime
    last_used: datetime
    total_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    is_healthy: bool = True


@dataclass
class PoolConfig:
    """Конфигурация пула соединений"""
    max_connections: int = 50
    min_connections: int = 5
    max_idle_time: int = 300  # 5 минут
    health_check_interval: int = 60  # 1 минута
    connection_timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 5


class ConnectionPool:
    """Асинхронный пул соединений для Telegram клиентов"""
    
    def __init__(self, config: PoolConfig = None):
        self.config = config or PoolConfig()
        
        # Основные структуры данных
        self.active_connections: Dict[str, TelegramClient] = {}
        self.idle_connections: deque = deque()
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        
        # Управление пулом
        self.semaphore = asyncio.Semaphore(self.config.max_connections)
        self.lock = asyncio.Lock()
        self.shutdown_event = asyncio.Event()
        
        # Задачи фонового обслуживания
        self.health_check_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # Статистика
        self.stats = {
            'total_created': 0,
            'total_destroyed': 0,
            'current_active': 0,
            'current_idle': 0,
            'pool_hits': 0,
            'pool_misses': 0
        }
        
        logger.info(f"Инициализирован пул соединений: max={self.config.max_connections}")

    async def start(self):
        """Запускает пул соединений"""
        logger.info("Запуск пула соединений...")
        
        # Запускаем фоновые задачи
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info("Пул соединений запущен")

    async def stop(self):
        """Останавливает пул соединений"""
        logger.info("Остановка пула соединений...")
        
        # Сигнализируем о завершении
        self.shutdown_event.set()
        
        # Отменяем фоновые задачи
        if self.health_check_task:
            self.health_check_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()
        
        # Закрываем все соединения
        await self._close_all_connections()
        
        logger.info("Пул соединений остановлен")

    @asynccontextmanager
    async def get_connection(self, account_id: str, account_data: Dict[str, Any]):
        """
        Контекстный менеджер для получения соединения
        
        Args:
            account_id: Идентификатор аккаунта
            account_data: Данные аккаунта (api_id, api_hash, прокси и т.д.)
        """
        connection = None
        start_time = datetime.now()
        
        try:
            async with self.semaphore:
                connection = await self._acquire_connection(account_id, account_data)
                
                if connection:
                    self.stats['pool_hits'] += 1
                    self._update_metrics(account_id, start_time, success=True)
                else:
                    self.stats['pool_misses'] += 1
                    raise RuntimeError(f"Не удалось получить соединение для {account_id}")
                
                yield connection
                
        except Exception as e:
            self._update_metrics(account_id, start_time, success=False)
            logger.error(f"Ошибка при работе с соединением {account_id}: {e}")
            raise
        finally:
            if connection:
                await self._release_connection(account_id, connection)

    async def _acquire_connection(self, account_id: str, account_data: Dict[str, Any]) -> Optional[TelegramClient]:
        """Получает соединение из пула или создает новое"""
        async with self.lock:
            # Проверяем активные соединения
            if account_id in self.active_connections:
                client = self.active_connections[account_id]
                if await self._is_connection_healthy(client):
                    return client
                else:
                    # Удаляем нездоровое соединение
                    await self._destroy_connection(account_id)
            
            # Создаем новое соединение
            client = await self._create_connection(account_id, account_data)
            if client:
                self.active_connections[account_id] = client
                self.stats['current_active'] += 1
                return client
            
            return None

    async def _release_connection(self, account_id: str, connection: TelegramClient):
        """Возвращает соединение в пул"""
        async with self.lock:
            if account_id in self.active_connections:
                # Обновляем метрики
                if account_id in self.connection_metrics:
                    self.connection_metrics[account_id].last_used = datetime.now()

    async def _create_connection(self, account_id: str, account_data: Dict[str, Any]) -> Optional[TelegramClient]:
        """Создает новое соединение"""
        try:
            api_id = int(account_data['api_id'])
            api_hash = account_data['api_hash']
            
            # Настройка прокси
            proxy = None
            if account_data.get('proxy_address') and account_data.get('proxy_port'):
                proxy = {
                    'proxy_type': socks.SOCKS5,
                    'addr': account_data['proxy_address'],
                    'port': int(account_data['proxy_port']),
                    'username': account_data.get('proxy_user'),
                    'password': account_data.get('proxy_pass'),
                    'rdns': True
                }
            
            # Создаем клиент
            session_path = f"sessions/{account_id}.session"
            client = TelegramClient(
                session_path,
                api_id,
                api_hash,
                proxy=proxy,
                timeout=self.config.connection_timeout,
                **TELEGRAM_CONFIG
            )
            
            # Подключаемся
            await client.connect()
            
            # Создаем метрики
            self.connection_metrics[account_id] = ConnectionMetrics(
                created_at=datetime.now(),
                last_used=datetime.now()
            )
            
            self.stats['total_created'] += 1
            logger.info(f"Создано соединение для {account_id}")
            
            return client
            
        except Exception as e:
            logger.error(f"Ошибка создания соединения для {account_id}: {e}")
            return None

    async def _is_connection_healthy(self, client: TelegramClient) -> bool:
        """Проверяет здоровье соединения"""
        try:
            if not client.is_connected():
                return False
            
            # Простая проверка - получение информации о себе
            await asyncio.wait_for(client.get_me(), timeout=5.0)
            return True
            
        except Exception:
            return False

    async def _destroy_connection(self, account_id: str):
        """Уничтожает соединение"""
        if account_id in self.active_connections:
            client = self.active_connections[account_id]
            await self._destroy_connection_object(client)
            del self.active_connections[account_id]
            self.stats['current_active'] -= 1
        
        if account_id in self.connection_metrics:
            del self.connection_metrics[account_id]

    async def _destroy_connection_object(self, client: TelegramClient):
        """Уничтожает объект соединения"""
        try:
            if client.is_connected():
                await client.disconnect()
            self.stats['total_destroyed'] += 1
        except Exception as e:
            logger.warning(f"Ошибка при закрытии соединения: {e}")

    async def _close_all_connections(self):
        """Закрывает все соединения"""
        # Закрываем активные соединения
        for account_id in list(self.active_connections.keys()):
            await self._destroy_connection(account_id)
        
        # Закрываем idle соединения
        while self.idle_connections:
            client = self.idle_connections.popleft()
            await self._destroy_connection_object(client)
            self.stats['current_idle'] -= 1

    def _update_metrics(self, account_id: str, start_time: datetime, success: bool):
        """Обновляет метрики соединения"""
        if account_id not in self.connection_metrics:
            return
        
        metrics = self.connection_metrics[account_id]
        metrics.total_requests += 1
        
        if not success:
            metrics.failed_requests += 1
            metrics.is_healthy = False
        else:
            # Обновляем среднее время отклика
            response_time = (datetime.now() - start_time).total_seconds()
            if metrics.avg_response_time == 0:
                metrics.avg_response_time = response_time
            else:
                metrics.avg_response_time = (metrics.avg_response_time + response_time) / 2

    async def _health_check_loop(self):
        """Фоновая проверка здоровья соединений"""
        while not self.shutdown_event.is_set():
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.config.health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в health check: {e}")

    async def _cleanup_loop(self):
        """Фоновая очистка неиспользуемых соединений"""
        while not self.shutdown_event.is_set():
            try:
                await self._cleanup_idle_connections()
                await asyncio.sleep(60)  # Проверяем каждую минуту
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в cleanup: {e}")

    async def _perform_health_checks(self):
        """Выполняет проверку здоровья всех соединений"""
        unhealthy_accounts = []
        
        for account_id, client in self.active_connections.items():
            if not await self._is_connection_healthy(client):
                unhealthy_accounts.append(account_id)
        
        # Удаляем нездоровые соединения
        for account_id in unhealthy_accounts:
            logger.warning(f"Удаляем нездоровое соединение: {account_id}")
            await self._destroy_connection(account_id)

    async def _cleanup_idle_connections(self):
        """Очищает неиспользуемые соединения"""
        now = datetime.now()
        max_idle = timedelta(seconds=self.config.max_idle_time)
        
        # Проверяем idle соединения
        connections_to_remove = []
        for i, client in enumerate(self.idle_connections):
            # Здесь нужна логика определения времени последнего использования
            # Для упрощения удаляем старые соединения
            if len(self.idle_connections) > self.config.min_connections:
                connections_to_remove.append(i)
        
        # Удаляем старые соединения
        for i in reversed(connections_to_remove):
            client = self.idle_connections[i]
            del self.idle_connections[i]
            await self._destroy_connection_object(client)
            self.stats['current_idle'] -= 1

    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику пула"""
        return {
            **self.stats,
            'memory_usage': psutil.Process().memory_info().rss / 1024 / 1024,  # MB
            'connection_metrics': {
                account_id: {
                    'total_requests': metrics.total_requests,
                    'failed_requests': metrics.failed_requests,
                    'success_rate': (metrics.total_requests - metrics.failed_requests) / max(metrics.total_requests, 1),
                    'avg_response_time': metrics.avg_response_time,
                    'is_healthy': metrics.is_healthy
                }
                for account_id, metrics in self.connection_metrics.items()
            }
        }


# Глобальный экземпляр пула
connection_pool = ConnectionPool()
