"""
Система управления памятью и кэширования для оптимизации производительности
Включает LRU кэш, слабые ссылки и автоматическую очистку памяти
"""

import asyncio
import gc
import logging
import weakref
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, TypeVar, Generic
from dataclasses import dataclass, field
from functools import wraps, lru_cache
from collections import OrderedDict
import threading
import time

logger = logging.getLogger(__name__)

T = TypeVar('T')


@dataclass
class CacheEntry:
    """Запись в кэше"""
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    size_bytes: int = 0


@dataclass
class MemoryStats:
    """Статистика использования памяти"""
    total_memory_mb: float
    used_memory_mb: float
    cache_memory_mb: float
    cache_hit_rate: float
    cache_entries: int
    gc_collections: int


class LRUCache(Generic[T]):
    """Потокобезопасный LRU кэш с TTL и ограничением по размеру"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600, max_memory_mb: int = 100):
        self.max_size = max_size
        self.ttl = timedelta(seconds=ttl_seconds)
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        
        # Статистика
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        self.current_memory_bytes = 0
        
        # Фоновая очистка
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Запускает фоновую задачу очистки"""
        try:
            loop = asyncio.get_event_loop()
            self._cleanup_task = loop.create_task(self._cleanup_loop())
        except RuntimeError:
            # Нет активного event loop
            pass
    
    async def _cleanup_loop(self):
        """Фоновая очистка устаревших записей"""
        while True:
            try:
                await asyncio.sleep(300)  # Каждые 5 минут
                self._cleanup_expired()
                self._enforce_memory_limit()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в cleanup loop: {e}")
    
    def get(self, key: str) -> Optional[T]:
        """Получает значение из кэша"""
        with self._lock:
            if key not in self._cache:
                self.misses += 1
                return None
            
            entry = self._cache[key]
            
            # Проверяем TTL
            if datetime.now() - entry.created_at > self.ttl:
                del self._cache[key]
                self.current_memory_bytes -= entry.size_bytes
                self.misses += 1
                return None
            
            # Обновляем статистику доступа
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            # Перемещаем в конец (most recently used)
            self._cache.move_to_end(key)
            
            self.hits += 1
            return entry.value
    
    def put(self, key: str, value: T, size_hint: int = 0) -> bool:
        """Добавляет значение в кэш"""
        with self._lock:
            now = datetime.now()
            
            # Оцениваем размер если не указан
            if size_hint == 0:
                size_hint = self._estimate_size(value)
            
            # Проверяем ограничения
            if size_hint > self.max_memory_bytes:
                logger.warning(f"Объект слишком большой для кэша: {size_hint} bytes")
                return False
            
            # Удаляем старую запись если есть
            if key in self._cache:
                old_entry = self._cache[key]
                self.current_memory_bytes -= old_entry.size_bytes
                del self._cache[key]
            
            # Освобождаем место если нужно
            while (len(self._cache) >= self.max_size or 
                   self.current_memory_bytes + size_hint > self.max_memory_bytes):
                if not self._cache:
                    break
                self._evict_lru()
            
            # Добавляем новую запись
            entry = CacheEntry(
                value=value,
                created_at=now,
                last_accessed=now,
                size_bytes=size_hint
            )
            
            self._cache[key] = entry
            self.current_memory_bytes += size_hint
            
            return True
    
    def _evict_lru(self):
        """Удаляет наименее используемую запись"""
        if self._cache:
            key, entry = self._cache.popitem(last=False)
            self.current_memory_bytes -= entry.size_bytes
            self.evictions += 1
    
    def _cleanup_expired(self):
        """Очищает устаревшие записи"""
        with self._lock:
            now = datetime.now()
            expired_keys = []
            
            for key, entry in self._cache.items():
                if now - entry.created_at > self.ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                entry = self._cache[key]
                self.current_memory_bytes -= entry.size_bytes
                del self._cache[key]
            
            if expired_keys:
                logger.debug(f"Удалено {len(expired_keys)} устаревших записей из кэша")
    
    def _enforce_memory_limit(self):
        """Принудительно освобождает память если превышен лимит"""
        with self._lock:
            while self.current_memory_bytes > self.max_memory_bytes and self._cache:
                self._evict_lru()
    
    def _estimate_size(self, obj: Any) -> int:
        """Оценивает размер объекта в байтах"""
        try:
            import sys
            return sys.getsizeof(obj)
        except:
            # Грубая оценка
            if isinstance(obj, str):
                return len(obj.encode('utf-8'))
            elif isinstance(obj, (list, tuple)):
                return sum(self._estimate_size(item) for item in obj)
            elif isinstance(obj, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) 
                          for k, v in obj.items())
            else:
                return 1024  # Дефолтная оценка
    
    def clear(self):
        """Очищает весь кэш"""
        with self._lock:
            self._cache.clear()
            self.current_memory_bytes = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику кэша"""
        with self._lock:
            total_requests = self.hits + self.misses
            hit_rate = self.hits / max(total_requests, 1)
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'memory_mb': self.current_memory_bytes / 1024 / 1024,
                'max_memory_mb': self.max_memory_bytes / 1024 / 1024,
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': hit_rate,
                'evictions': self.evictions
            }


class WeakValueCache:
    """Кэш со слабыми ссылками для автоматической очистки"""
    
    def __init__(self):
        self._cache: weakref.WeakValueDictionary = weakref.WeakValueDictionary()
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Получает значение из кэша"""
        with self._lock:
            return self._cache.get(key)
    
    def put(self, key: str, value: Any):
        """Добавляет значение в кэш"""
        with self._lock:
            try:
                self._cache[key] = value
            except TypeError:
                # Объект не поддерживает слабые ссылки
                pass
    
    def clear(self):
        """Очищает кэш"""
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        """Возвращает размер кэша"""
        with self._lock:
            return len(self._cache)


class MemoryManager:
    """Менеджер памяти для оптимизации использования ресурсов"""
    
    def __init__(self):
        # Кэши
        self.message_cache = LRUCache[Dict](max_size=1000, ttl_seconds=1800)  # 30 минут
        self.group_cache = LRUCache[Dict](max_size=100, ttl_seconds=3600)    # 1 час
        self.user_cache = WeakValueCache()
        
        # Статистика
        self.gc_collections = 0
        self.last_gc_time = datetime.now()
        
        # Мониторинг
        self._monitor_task: Optional[asyncio.Task] = None
        self._start_monitoring()
    
    def _start_monitoring(self):
        """Запускает мониторинг памяти"""
        try:
            loop = asyncio.get_event_loop()
            self._monitor_task = loop.create_task(self._memory_monitor_loop())
        except RuntimeError:
            pass
    
    async def _memory_monitor_loop(self):
        """Фоновый мониторинг использования памяти"""
        while True:
            try:
                await asyncio.sleep(60)  # Каждую минуту
                
                # Получаем статистику памяти
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_percent = process.memory_percent()
                
                # Если использование памяти высокое, запускаем очистку
                if memory_percent > 80:
                    logger.warning(f"Высокое использование памяти: {memory_percent:.1f}%")
                    await self.cleanup_memory()
                
                # Логируем статистику каждые 10 минут
                if datetime.now().minute % 10 == 0:
                    stats = self.get_memory_stats()
                    logger.info(f"Память: {stats.used_memory_mb:.1f}MB, "
                              f"кэш: {stats.cache_memory_mb:.1f}MB, "
                              f"hit rate: {stats.cache_hit_rate:.2f}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Ошибка в мониторе памяти: {e}")
    
    async def cleanup_memory(self):
        """Принудительная очистка памяти"""
        logger.info("Запуск очистки памяти...")
        
        # Очищаем кэши
        self.message_cache.clear()
        self.group_cache.clear()
        self.user_cache.clear()
        
        # Запускаем сборщик мусора
        collected = gc.collect()
        self.gc_collections += 1
        self.last_gc_time = datetime.now()
        
        logger.info(f"Очистка памяти завершена, собрано {collected} объектов")
    
    def cache_message(self, message_id: int, message_data: Dict[str, Any]):
        """Кэширует данные сообщения"""
        key = f"msg_{message_id}"
        size_hint = len(str(message_data))
        self.message_cache.put(key, message_data, size_hint)
    
    def get_cached_message(self, message_id: int) -> Optional[Dict[str, Any]]:
        """Получает кэшированные данные сообщения"""
        key = f"msg_{message_id}"
        return self.message_cache.get(key)
    
    def cache_group_info(self, group_id: str, group_data: Dict[str, Any]):
        """Кэширует информацию о группе"""
        key = f"group_{group_id}"
        size_hint = len(str(group_data))
        self.group_cache.put(key, group_data, size_hint)
    
    def get_cached_group_info(self, group_id: str) -> Optional[Dict[str, Any]]:
        """Получает кэшированную информацию о группе"""
        key = f"group_{group_id}"
        return self.group_cache.get(key)
    
    def cache_user(self, user_id: int, user_obj: Any):
        """Кэширует объект пользователя со слабой ссылкой"""
        key = f"user_{user_id}"
        self.user_cache.put(key, user_obj)
    
    def get_cached_user(self, user_id: int) -> Optional[Any]:
        """Получает кэшированный объект пользователя"""
        key = f"user_{user_id}"
        return self.user_cache.get(key)
    
    def get_memory_stats(self) -> MemoryStats:
        """Возвращает статистику использования памяти"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        # Статистика кэшей
        msg_stats = self.message_cache.get_stats()
        group_stats = self.group_cache.get_stats()
        
        total_cache_memory = msg_stats['memory_mb'] + group_stats['memory_mb']
        total_requests = msg_stats['hits'] + msg_stats['misses'] + group_stats['hits'] + group_stats['misses']
        total_hits = msg_stats['hits'] + group_stats['hits']
        hit_rate = total_hits / max(total_requests, 1)
        
        return MemoryStats(
            total_memory_mb=memory_info.rss / 1024 / 1024,
            used_memory_mb=memory_info.rss / 1024 / 1024,
            cache_memory_mb=total_cache_memory,
            cache_hit_rate=hit_rate,
            cache_entries=msg_stats['size'] + group_stats['size'] + self.user_cache.size(),
            gc_collections=self.gc_collections
        )
    
    def stop(self):
        """Останавливает менеджер памяти"""
        if self._monitor_task:
            self._monitor_task.cancel()
        
        # Очищаем все кэши
        self.message_cache.clear()
        self.group_cache.clear()
        self.user_cache.clear()


def memory_cached(ttl_seconds: int = 3600, max_size: int = 100):
    """Декоратор для кэширования результатов функций"""
    cache = LRUCache(max_size=max_size, ttl_seconds=ttl_seconds)
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Создаем ключ кэша
            key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Проверяем кэш
            result = cache.get(key)
            if result is not None:
                return result
            
            # Выполняем функцию
            result = await func(*args, **kwargs)
            
            # Кэшируем результат
            cache.put(key, result)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Создаем ключ кэша
            key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Проверяем кэш
            result = cache.get(key)
            if result is not None:
                return result
            
            # Выполняем функцию
            result = func(*args, **kwargs)
            
            # Кэшируем результат
            cache.put(key, result)
            
            return result
        
        # Возвращаем соответствующий wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# Глобальный экземпляр менеджера памяти
memory_manager = MemoryManager()
