#!/usr/bin/env python3
"""
Тест прокси для проверки подключения
"""

import requests
import socks
import socket
from urllib.parse import urlparse

def test_proxy():
    """Тестирует SOCKS5 прокси"""
    
    # Данные прокси
    proxy_host = "*************"
    proxy_port = 64389
    proxy_user = "Y1gC3qxS"
    proxy_pass = "Wu4tmdT2"
    
    print("🔍 Тестирование SOCKS5 прокси...")
    print(f"Прокси: {proxy_host}:{proxy_port}")
    print(f"Пользователь: {proxy_user}")
    
    try:
        # Настройка прокси для requests
        proxies = {
            'http': f'socks5://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}',
            'https': f'socks5://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}'
        }
        
        # Тест 1: Проверка IP адреса
        print("\n📡 Тест 1: Проверка IP адреса...")
        response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            ip_data = response.json()
            print(f"✅ Успешно! Ваш IP через прокси: {ip_data['origin']}")
        else:
            print(f"❌ Ошибка HTTP: {response.status_code}")
            return False
        
        # Тест 2: Проверка скорости
        print("\n⚡ Тест 2: Проверка скорости...")
        import time
        start_time = time.time()
        response = requests.get('https://httpbin.org/get', proxies=proxies, timeout=10)
        end_time = time.time()
        
        if response.status_code == 200:
            ping = (end_time - start_time) * 1000
            print(f"✅ Пинг: {ping:.0f}ms")
            
            if ping < 1000:
                print("🚀 Отличная скорость!")
            elif ping < 3000:
                print("👍 Хорошая скорость")
            else:
                print("⚠️ Медленная скорость, но работает")
        
        # Тест 3: Проверка доступности Telegram
        print("\n📱 Тест 3: Проверка доступности Telegram API...")
        try:
            response = requests.get('https://api.telegram.org', proxies=proxies, timeout=10)
            if response.status_code in [200, 401, 404]:  # Любой ответ от Telegram - хорошо
                print("✅ Telegram API доступен через прокси")
            else:
                print(f"⚠️ Неожиданный ответ от Telegram: {response.status_code}")
        except Exception as e:
            print(f"❌ Ошибка доступа к Telegram API: {e}")
        
        print("\n🎉 Прокси работает корректно!")
        return True
        
    except requests.exceptions.ProxyError as e:
        print(f"❌ Ошибка прокси: {e}")
        print("💡 Проверьте правильность данных прокси")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ Таймаут подключения: {e}")
        print("💡 Прокси может быть медленным или недоступным")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

if __name__ == "__main__":
    print("🛡️ ТЕСТ ПРОКСИ ДЛЯ TELEGRAM ФЕРМЫ")
    print("=" * 50)
    
    success = test_proxy()
    
    if success:
        print("\n✅ Прокси готов к использованию!")
        print("📝 Следующий шаг: получите API ключи и обновите accounts.csv")
    else:
        print("\n❌ Прокси не работает!")
        print("📞 Обратитесь к провайдеру прокси для решения проблемы")
