#!/usr/bin/env python3
"""
🛡️ БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ ВАШЕГО АККАУНТА
Тестирование аккаунта Михаила Агапова (+*************)
"""

import asyncio
import logging
from datetime import datetime
from telethon import TelegramClient
from telethon.errors import FloodWaitError
import socks

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - 🛡️ SAFE_TEST - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class SafeAccountTester:
    """Безопасное тестирование аккаунта"""
    
    def __init__(self):
        # Данные аккаунта
        self.phone = "+*************"
        self.api_id = ********
        self.api_hash = "320690fc08a869f3e270f82a31954fa7"
        
        # Прокси SOCKS5
        self.proxy_config = {
            'proxy_type': socks.SOCKS5,
            'addr': '176.103.95.80',
            'port': 64389,
            'username': 'Y1gC3qxS',
            'password': 'Wu4tmdT2',
            'rdns': True
        }
        
        self.client = None
    
    async def connect(self):
        """Подключается к Telegram"""
        try:
            session_path = f"sessions/{self.phone}_proxy.session"
            
            self.client = TelegramClient(
                session_path,
                self.api_id,
                self.api_hash,
                proxy=self.proxy_config,
                device_model="Desktop",
                system_version="Windows 10",
                app_version="4.8.4",
                lang_code="en"
            )
            
            print("🔗 Подключение к Telegram через прокси...")
            await self.client.connect()
            
            if self.client.is_connected():
                print("✅ Подключение успешно!")
                return True
            else:
                print("❌ Не удалось подключиться")
                return False
                
        except Exception as e:
            print(f"❌ Ошибка подключения: {e}")
            return False
    
    async def get_account_info(self):
        """Получает информацию об аккаунте"""
        try:
            me = await self.client.get_me()
            
            print(f"\n👤 ИНФОРМАЦИЯ ОБ АККАУНТЕ:")
            print(f"   Имя: {me.first_name}")
            if me.last_name:
                print(f"   Фамилия: {me.last_name}")
            print(f"   Телефон: {me.phone}")
            print(f"   ID: {me.id}")
            if me.username:
                print(f"   Username: @{me.username}")
            
            return True
            
        except Exception as e:
            print(f"❌ Ошибка получения информации: {e}")
            return False
    
    async def get_dialogs_info(self):
        """Получает информацию о диалогах"""
        try:
            print(f"\n💬 ВАШИ ДИАЛОГИ (первые 10):")
            
            count = 0
            async for dialog in self.client.iter_dialogs(limit=10):
                count += 1
                
                if dialog.is_group:
                    type_icon = "👥"
                    type_name = "Группа"
                elif dialog.is_channel:
                    type_icon = "📢"
                    type_name = "Канал"
                else:
                    type_icon = "👤"
                    type_name = "Личный"
                
                print(f"   {count}. {type_icon} {dialog.name} ({type_name})")
                if hasattr(dialog.entity, 'username') and dialog.entity.username:
                    print(f"      Username: @{dialog.entity.username}")
            
            print(f"\n💡 Всего найдено {count} диалогов")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка получения диалогов: {e}")
            return False
    
    async def test_basic_operations(self):
        """Тестирует базовые операции"""
        try:
            print(f"\n🧪 ТЕСТИРОВАНИЕ БАЗОВЫХ ОПЕРАЦИЙ:")
            
            # Тест 1: Получение информации о себе
            print("   1. Получение информации о себе...")
            me = await self.client.get_me()
            print("   ✅ Успешно")
            
            # Тест 2: Получение диалогов
            print("   2. Получение списка диалогов...")
            dialogs = await self.client.get_dialogs(limit=5)
            print(f"   ✅ Получено {len(dialogs)} диалогов")
            
            # Тест 3: Проверка подключения
            print("   3. Проверка стабильности подключения...")
            if self.client.is_connected():
                print("   ✅ Подключение стабильно")
            else:
                print("   ❌ Подключение нестабильно")
                return False
            
            return True
            
        except FloodWaitError as e:
            print(f"   ❌ FloodWait: нужно подождать {e.seconds} секунд")
            return False
        except Exception as e:
            print(f"   ❌ Ошибка: {e}")
            return False
    
    async def disconnect(self):
        """Отключается от Telegram"""
        if self.client:
            await self.client.disconnect()
            print("🔌 Отключение от Telegram")
    
    async def run_safe_test(self):
        """Запускает безопасное тестирование"""
        print("🛡️ БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ АККАУНТА")
        print("=" * 60)
        print(f"📞 Номер: {self.phone}")
        print(f"🌐 Прокси: {self.proxy_config['addr']}:{self.proxy_config['port']}")
        print(f"⏰ Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        success = True
        
        # Шаг 1: Подключение
        if not await self.connect():
            return False
        
        try:
            # Шаг 2: Информация об аккаунте
            if not await self.get_account_info():
                success = False
            
            # Шаг 3: Информация о диалогах
            if not await self.get_dialogs_info():
                success = False
            
            # Шаг 4: Тестирование базовых операций
            if not await self.test_basic_operations():
                success = False
            
        finally:
            await self.disconnect()
        
        # Результат
        if success:
            print(f"\n🎉 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО УСПЕШНО!")
            print("=" * 60)
            print("✅ Аккаунт работает корректно")
            print("✅ Прокси функционирует")
            print("✅ Все базовые операции доступны")
            print()
            print("📋 ГОТОВ К ИСПОЛЬЗОВАНИЮ:")
            print("1. Создайте тестовую группу в Telegram")
            print("2. Добавьте её в конфигурацию")
            print("3. Запустите ферму с безопасными лимитами")
            print()
            print("⚠️ ПОМНИТЕ:")
            print("   - Используйте только безопасные лимиты")
            print("   - Тестируйте на собственных группах")
            print("   - Мониторьте активность аккаунта")
        else:
            print(f"\n❌ ТЕСТИРОВАНИЕ НЕ ЗАВЕРШЕНО")
            print("Обнаружены проблемы, проверьте логи выше")
        
        return success

async def main():
    """Главная функция"""
    tester = SafeAccountTester()
    await tester.run_safe_test()

if __name__ == "__main__":
    asyncio.run(main())
