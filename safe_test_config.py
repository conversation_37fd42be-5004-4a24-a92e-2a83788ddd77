"""
БЕЗОПАСНАЯ конфигурация для тестирования личного аккаунта
Минимальные лимиты и максимальные задержки для предотвращения блокировки
"""

# БЕЗОПАСНЫЕ лимиты для личного аккаунта
SAFE_DAILY_LIMITS = {
    "comments": 1,      # ТОЛЬКО 1 комментарий в день
    "reactions": 2,     # ТОЛЬКО 2 реакции в день  
    "dialogues": 0,     # ПОЛНОСТЬЮ отключены диалоги
}

# БЕЗОПАСНЫЕ задержки (большие интервалы)
SAFE_ACTION_DELAYS = {
    "min_delay": 30 * 60,   # Минимум 30 минут между действиями
    "max_delay": 120 * 60,  # Максимум 2 часа между действиями
}

# Задержки имитации набора текста (более естественные)
SAFE_TYPING_DELAYS = {
    "min_typing_time": 5,   # Минимум 5 секунд "набора"
    "max_typing_time": 15,  # Максимум 15 секунд "набора"
}

# ТОЛЬКО безопасные реакции (избегаем спорных)
SAFE_REACTIONS = [
    "👍", "❤️", "😊", "👏", "🔥"  # Только позитивные реакции
]

# Безопасные шаблоны комментариев (нейтральные)
SAFE_COMMENT_TEMPLATES = [
    "Интересно!",
    "Спасибо за информацию",
    "Полезно знать",
    "Хорошая тема",
    "Согласен",
]

# Настройки для предотвращения флуд-контроля
FLOOD_PROTECTION = {
    "max_actions_per_hour": 1,      # Максимум 1 действие в час
    "max_actions_per_day": 3,       # Максимум 3 действия в день
    "cooldown_on_error": 60 * 60,   # 1 час паузы при любой ошибке
}

# Мониторинг безопасности
SAFETY_MONITORING = {
    "log_all_actions": True,        # Логировать все действия
    "stop_on_flood_wait": True,     # Остановка при FloodWait
    "stop_on_any_error": True,      # Остановка при любой ошибке
    "max_errors_per_day": 1,        # Максимум 1 ошибка в день
}

# Тестовые группы (ОБЯЗАТЕЛЬНО замените на свои!)
TEST_GROUPS = [
    "@your_test_group",  # ЗАМЕНИТЕ на свою тестовую группу!
    # Добавьте больше групп если нужно
]

print("🛡️ Загружена БЕЗОПАСНАЯ конфигурация для тестирования личного аккаунта")
print("⚠️  ВНИМАНИЕ: Обязательно укажите свою тестовую группу в TEST_GROUPS!")
