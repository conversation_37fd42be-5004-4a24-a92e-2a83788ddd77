#!/usr/bin/env python3
"""
Тестирование расширенных возможностей Telegram фермы v2.0
Проверяет AI генерацию, масштабирование и мониторинг
"""

import asyncio
import sys
import time
from datetime import datetime

# Тестирование AI модулей
def test_ai_generator():
    """Тестирует AI генератор"""
    print("🧪 Тестирование AI генератора...")
    
    try:
        from modules.ai_generator import ai_generator
        
        # Проверяем инициализацию
        stats = ai_generator.get_stats()
        print(f"✅ AI провайдеры инициализированы: {len(stats['providers'])}")
        
        # Проверяем fallback
        assert ai_generator.fallback_enabled, "Fallback должен быть включен"
        print("✅ Fallback механизм активен")
        
        # Тестируем настройки
        ai_generator.set_fallback_enabled(False)
        assert not ai_generator.fallback_enabled, "Fallback должен отключиться"
        ai_generator.set_fallback_enabled(True)
        print("✅ Управление fallback работает")
        
        print("✅ AI генератор работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка в AI генераторе: {e}\n")


async def test_ai_generation():
    """Тестирует генерацию текстов"""
    print("🧪 Тестирование генерации текстов...")
    
    try:
        from modules.ai_generator import ai_generator
        
        # Тест генерации комментария
        test_post = "Сегодня изучал новые возможности Python. Очень интересный язык программирования!"
        
        comment = await ai_generator.generate_comment(
            post_text=test_post,
            group_context="@python_developers"
        )
        
        print(f"✅ Сгенерированный комментарий: '{comment}'")
        assert len(comment) > 5, "Комментарий слишком короткий"
        assert len(comment) < 200, "Комментарий слишком длинный"
        
        # Тест генерации ответа
        original_comment = "Согласен, Python действительно мощный инструмент!"
        
        reply = await ai_generator.generate_reply(
            original_comment=original_comment,
            post_context=test_post
        )
        
        print(f"✅ Сгенерированный ответ: '{reply}'")
        assert len(reply) > 3, "Ответ слишком короткий"
        assert len(reply) < 150, "Ответ слишком длинный"
        
        print("✅ Генерация текстов работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка в генерации текстов: {e}\n")


def test_context_analyzer():
    """Тестирует анализатор контекста"""
    print("🧪 Тестирование анализатора контекста...")
    
    try:
        from modules.context_analyzer import context_analyzer, ContentType, Sentiment
        
        # Тест технического контента
        tech_text = "Новая версия Python 3.12 включает улучшения производительности и новые возможности для разработчиков"
        analysis = context_analyzer.analyze_content(tech_text)
        
        print(f"✅ Технический контент: тип={analysis.content_type.value}, тональность={analysis.sentiment.value}")
        assert analysis.content_type == ContentType.TECH, "Должен определить как технический контент"
        
        # Тест бизнес контента
        business_text = "Стартап привлек 5 миллионов долларов инвестиций для развития маркетинговой стратегии"
        analysis = context_analyzer.analyze_content(business_text)
        
        print(f"✅ Бизнес контент: тип={analysis.content_type.value}, тональность={analysis.sentiment.value}")
        assert analysis.content_type == ContentType.BUSINESS, "Должен определить как бизнес контент"
        
        # Тест позитивной тональности
        positive_text = "Отличная новость! Проект завершен успешно, все довольны результатом!"
        analysis = context_analyzer.analyze_content(positive_text)
        
        print(f"✅ Позитивный контент: тональность={analysis.sentiment.value}")
        assert analysis.sentiment == Sentiment.POSITIVE, "Должен определить позитивную тональность"
        
        # Тест вопроса
        question_text = "Как вы думаете, какой фреймворк лучше выбрать для нового проекта?"
        analysis = context_analyzer.analyze_content(question_text)
        
        print(f"✅ Вопрос: тональность={analysis.sentiment.value}")
        assert analysis.sentiment == Sentiment.QUESTION, "Должен определить как вопрос"
        
        print("✅ Анализатор контекста работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка в анализаторе контекста: {e}\n")


def test_scalable_farm():
    """Тестирует масштабируемую ферму"""
    print("🧪 Тестирование масштабируемой фермы...")
    
    try:
        from modules.scalable_farm import (
            ScalableFarmManager, Task, TaskType, AccountStatus,
            TaskQueue, ConnectionPool, LoadBalancer
        )
        
        # Тест очереди задач
        queue = TaskQueue(max_size=100)
        
        task1 = Task("test1", TaskType.COMMENT, "+***********", "@test_group", priority=1)
        task2 = Task("test2", TaskType.REACTION, "+***********", "@test_group", priority=2)
        
        assert queue.put(task1), "Должен добавить задачу"
        assert queue.put(task2), "Должен добавить задачу"
        assert queue.size() == 2, "Размер очереди должен быть 2"
        
        # Проверяем приоритеты
        retrieved_task = queue.get()
        assert retrieved_task.priority == 2, "Должен вернуть задачу с высшим приоритетом"
        
        print("✅ Очередь задач работает корректно")
        
        # Тест балансировщика нагрузки
        balancer = LoadBalancer()
        balancer.register_account("+***********", "proxy1")
        balancer.register_account("+***********", "proxy1")
        balancer.register_account("+***********", "proxy2")
        
        best_account = balancer.get_best_account()
        assert best_account is not None, "Должен вернуть аккаунт"
        
        # Добавляем нагрузку и проверяем балансировку
        balancer.add_load(best_account, 1.0)
        new_best = balancer.get_best_account()
        
        print("✅ Балансировщик нагрузки работает корректно")
        
        # Тест менеджера фермы
        farm_manager = ScalableFarmManager(max_connections=10)
        farm_manager.register_account("+***********", "proxy1")
        farm_manager.register_account("+***********", "proxy2")
        
        stats = farm_manager.get_stats()
        assert stats["total_accounts"] == 2, "Должно быть 2 аккаунта"
        
        print("✅ Масштабируемая ферма работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка в масштабируемой ферме: {e}\n")


async def test_monitoring():
    """Тестирует систему мониторинга"""
    print("🧪 Тестирование системы мониторинга...")
    
    try:
        from modules.monitoring import MetricsCollector, AlertManager, FarmMonitor
        
        # Тест сборщика метрик
        collector = MetricsCollector()
        
        # Записываем тестовые метрики
        collector.record_metric("test.cpu", 75.5)
        collector.record_metric("test.memory", 60.2)
        collector.record_metric("test.cpu", 80.1)
        
        # Проверяем получение метрик
        latest_cpu = collector.get_latest_metric("test.cpu")
        assert latest_cpu is not None, "Должен вернуть метрику"
        assert latest_cpu.value == 80.1, "Должен вернуть последнее значение"
        
        # Проверяем среднее значение
        avg_cpu = collector.get_average("test.cpu", 60)
        assert avg_cpu is not None, "Должен вернуть среднее"
        assert abs(avg_cpu - 77.8) < 0.1, "Среднее должно быть около 77.8"
        
        print("✅ Сборщик метрик работает корректно")
        
        # Тест менеджера алертов
        alert_manager = AlertManager()
        alert_manager.add_rule("test.cpu", 70, "greater", "warning", "Высокая загрузка CPU")
        
        # Проверяем создание алерта
        await alert_manager.check_rules(collector)
        active_alerts = alert_manager.get_active_alerts()
        assert len(active_alerts) > 0, "Должен создать алерт"
        
        print("✅ Менеджер алертов работает корректно")
        
        # Тест главного монитора
        monitor = FarmMonitor()
        monitor.record_farm_metric("comments.total", 150)
        monitor.record_farm_metric("accounts.active", 25)
        
        health_report = monitor.get_health_report()
        assert "system_health" in health_report, "Должен содержать системное здоровье"
        assert "farm_metrics" in health_report, "Должен содержать метрики фермы"
        
        print("✅ Система мониторинга работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка в системе мониторинга: {e}\n")


def test_ai_config():
    """Тестирует конфигурацию AI"""
    print("🧪 Тестирование конфигурации AI...")
    
    try:
        from ai_config import (
            get_active_providers, get_prompt_for_content,
            validate_ai_response, clean_ai_response
        )
        
        # Тест получения провайдеров
        providers = get_active_providers()
        print(f"✅ Найдено активных провайдеров: {len(providers)}")
        
        # Тест получения промптов
        tech_prompt = get_prompt_for_content("tech", "comment")
        assert "{post_text}" in tech_prompt, "Промпт должен содержать плейсхолдер"
        
        business_prompt = get_prompt_for_content("business", "reply")
        assert "{original_comment}" in business_prompt, "Промпт должен содержать плейсхолдер"
        
        print("✅ Получение промптов работает корректно")
        
        # Тест валидации ответов
        valid_response = "Интересная статья, спасибо за информацию!"
        invalid_response = "Как ИИ, я не могу дать точный ответ на этот вопрос"
        
        assert validate_ai_response(valid_response), "Должен принять валидный ответ"
        assert not validate_ai_response(invalid_response), "Должен отклонить невалидный ответ"
        
        print("✅ Валидация ответов работает корректно")
        
        # Тест очистки ответов
        dirty_response = "Отличный пост! 😊 https://example.com   "
        clean_response = clean_ai_response(dirty_response)
        
        assert "😊" not in clean_response, "Должен удалить эмодзи"
        assert "https://example.com" not in clean_response, "Должен удалить URL"
        assert clean_response.strip() == clean_response, "Должен убрать лишние пробелы"
        
        print("✅ Очистка ответов работает корректно")
        print("✅ Конфигурация AI работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка в конфигурации AI: {e}\n")


def test_integration():
    """Тестирует интеграцию модулей"""
    print("🧪 Тестирование интеграции модулей...")
    
    try:
        # Проверяем что все модули импортируются
        from modules.ai_generator import ai_generator
        from modules.context_analyzer import context_analyzer
        from modules.scalable_farm import ScalableFarmManager
        from modules.monitoring import farm_monitor
        from ai_config import get_active_providers
        
        print("✅ Все модули успешно импортированы")
        
        # Проверяем базовую совместимость
        test_text = "Тестовый пост для проверки интеграции модулей"
        analysis = context_analyzer.analyze_content(test_text)
        
        assert analysis is not None, "Анализ должен вернуть результат"
        print("✅ Интеграция анализатора контекста работает")
        
        # Проверяем мониторинг
        farm_monitor.record_farm_metric("integration.test", 1.0)
        print("✅ Интеграция мониторинга работает")
        
        print("✅ Интеграция модулей работает корректно\n")
        
    except Exception as e:
        print(f"❌ Ошибка интеграции: {e}\n")


async def main():
    """Запускает все тесты"""
    print("🚀 Запуск тестов расширенных возможностей Telegram фермы v2.0\n")
    
    start_time = time.time()
    
    try:
        # Синхронные тесты
        test_ai_generator()
        test_context_analyzer()
        test_scalable_farm()
        test_ai_config()
        test_integration()
        
        # Асинхронные тесты
        await test_ai_generation()
        await test_monitoring()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("🎉 Все тесты пройдены успешно!")
        print(f"⏱️ Время выполнения: {duration:.2f} секунд")
        print("✅ Расширенные возможности готовы к использованию")
        
        # Выводим сводку возможностей
        print("\n📋 ДОСТУПНЫЕ ВОЗМОЖНОСТИ:")
        print("🤖 AI генерация текстов с fallback на Spintax")
        print("🔍 Контекстный анализ постов")
        print("⚡ Масштабирование до 100+ аккаунтов")
        print("📊 Система мониторинга и алертов")
        print("🌐 Веб-интерфейс управления")
        print("🔄 Балансировка нагрузки")
        print("📈 Метрики производительности")
        
    except Exception as e:
        print(f"❌ Критическая ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
