#!/usr/bin/env python3
"""
🚀 БЫСТРАЯ АВТОРИЗАЦИЯ
Быстро авторизует аккаунт для использования в веб-интерфейсе
"""

import asyncio
import logging
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError
import socks

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def quick_auth():
    """Быстрая авторизация аккаунта"""
    
    # Данные аккаунта
    phone = "+6283140647775"
    api_id = 24976030
    api_hash = "320690fc08a869f3e270f82a31954fa7"
    
    # Прокси SOCKS5
    proxy_config = {
        'proxy_type': socks.SOCKS5,
        'addr': '176.103.95.80',
        'port': 64389,
        'username': 'Y1gC3qxS',
        'password': 'Wu4tmdT2',
        'rdns': True
    }
    
    print("🚀 БЫСТРАЯ АВТОРИЗАЦИЯ")
    print("=" * 30)
    
    try:
        # Создаем клиент
        client = TelegramClient(
            f"sessions/{phone}.session",
            api_id,
            api_hash,
            proxy=proxy_config
        )
        
        await client.connect()
        
        if not await client.is_user_authorized():
            print("📱 Отправляем код...")
            await client.send_code_request(phone)
            
            code = input("Введите код: ")
            
            try:
                await client.sign_in(phone, code)
            except SessionPasswordNeededError:
                password = input("Введите пароль 2FA: ")
                await client.sign_in(password=password)
        
        # Проверяем
        me = await client.get_me()
        print(f"✅ Авторизован: {me.first_name}")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(quick_auth())
