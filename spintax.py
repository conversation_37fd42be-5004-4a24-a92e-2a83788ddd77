"""
Модуль для обработки Spintax-текстов
Позволяет генерировать вариативные сообщения из шаблонов
"""

import re
import random
from typing import List, Optional


def process_spintax(text: str) -> str:
    """
    Обрабатывает Spintax-текст и возвращает случайный вариант
    
    Пример:
    "{Привет|Добрый день|Здравствуйте}! {Отличный|Хороший} пост!"
    Может вернуть: "Добрый день! Хороший пост!"
    
    Args:
        text (str): Текст с Spintax-разметкой
        
    Returns:
        str: Обработанный текст со случайными вариантами
    """
    if not text:
        return ""
    
    # Паттерн для поиска Spintax-блоков {вариант1|вариант2|вариант3}
    pattern = r'\{([^{}]+)\}'
    
    def replace_spintax(match):
        """Заменяет найденный Spintax-блок на случайный вариант"""
        options = match.group(1).split('|')
        # Убираем пустые варианты и лишние пробелы
        options = [opt.strip() for opt in options if opt.strip()]
        
        if not options:
            return ""
        
        return random.choice(options)
    
    # Заменяем все Spintax-блоки
    while re.search(pattern, text):
        text = re.sub(pattern, replace_spintax, text)
    
    return text.strip()


def generate_comment(templates: List[str]) -> str:
    """
    Генерирует случайный комментарий из списка шаблонов
    
    Args:
        templates (List[str]): Список шаблонов с Spintax-разметкой
        
    Returns:
        str: Сгенерированный комментарий
    """
    if not templates:
        return "Интересно!"
    
    template = random.choice(templates)
    return process_spintax(template)


def generate_reply(templates: List[str]) -> str:
    """
    Генерирует случайный ответ из списка шаблонов
    
    Args:
        templates (List[str]): Список шаблонов ответов с Spintax-разметкой
        
    Returns:
        str: Сгенерированный ответ
    """
    if not templates:
        return "Согласен!"
    
    template = random.choice(templates)
    return process_spintax(template)


def validate_spintax(text: str) -> bool:
    """
    Проверяет корректность Spintax-разметки
    
    Args:
        text (str): Текст для проверки
        
    Returns:
        bool: True если разметка корректна, False иначе
    """
    if not text:
        return True
    
    # Проверяем парность скобок
    open_count = text.count('{')
    close_count = text.count('}')
    
    if open_count != close_count:
        return False
    
    # Проверяем что внутри каждого блока есть хотя бы один вариант
    pattern = r'\{([^{}]+)\}'
    matches = re.findall(pattern, text)
    
    for match in matches:
        options = [opt.strip() for opt in match.split('|') if opt.strip()]
        if not options:
            return False
    
    return True


def get_spintax_variants_count(text: str) -> int:
    """
    Подсчитывает общее количество возможных вариантов текста
    
    Args:
        text (str): Текст с Spintax-разметкой
        
    Returns:
        int: Количество возможных вариантов
    """
    if not text or not validate_spintax(text):
        return 1
    
    pattern = r'\{([^{}]+)\}'
    matches = re.findall(pattern, text)
    
    total_variants = 1
    for match in matches:
        options = [opt.strip() for opt in match.split('|') if opt.strip()]
        total_variants *= len(options)
    
    return total_variants


def preview_spintax_variants(text: str, max_variants: int = 10) -> List[str]:
    """
    Генерирует несколько вариантов текста для предварительного просмотра
    
    Args:
        text (str): Текст с Spintax-разметкой
        max_variants (int): Максимальное количество вариантов для генерации
        
    Returns:
        List[str]: Список сгенерированных вариантов
    """
    if not text or not validate_spintax(text):
        return [text]
    
    variants = set()
    attempts = 0
    max_attempts = max_variants * 10  # Избегаем бесконечного цикла
    
    while len(variants) < max_variants and attempts < max_attempts:
        variant = process_spintax(text)
        variants.add(variant)
        attempts += 1
    
    return list(variants)


# Пример использования
if __name__ == "__main__":
    # Тестирование функций
    test_text = "{Привет|Добрый день|Здравствуйте}! {Отличный|Хороший|Классный} пост, {спасибо|благодарю}!"
    
    print("Исходный шаблон:")
    print(test_text)
    print()
    
    print("Проверка корректности:", validate_spintax(test_text))
    print("Количество вариантов:", get_spintax_variants_count(test_text))
    print()
    
    print("Сгенерированные варианты:")
    for i in range(5):
        print(f"{i+1}. {process_spintax(test_text)}")
    
    print()
    print("Предварительный просмотр всех вариантов:")
    variants = preview_spintax_variants(test_text)
    for i, variant in enumerate(variants, 1):
        print(f"{i}. {variant}")
